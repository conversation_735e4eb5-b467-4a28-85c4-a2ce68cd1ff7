# คู่มือการแมป Profile Features กับ Database Tables

## ภาพรวม

ตามความต้องการ จะแยกการจัดเก็บข้อมูล Profile Features ดังนี้:
- **MERCHANTS Table**: เก็บข้อมูลส่วนใหญ่ที่เกี่ยวข้องกับร้านค้า
- **USERS Table**: เก็บเฉพาะ 2FA Google Authenticator

## 📊 **การแมป Features กับ Tables**

### 🏪 **MERCHANTS Table Features**

| Feature | ฟิลด์ใน merchants | คำอธิบาย |
|---------|------------------|----------|
| **Withdraw Pin Code** | `pin_code` | PIN Code 6 หลัก (เข้ารหัส) |
| | `pin_code_changed_date` | วันที่เปลี่ยน PIN ล่าสุด |
| **Profile Info** | `first_name`, `last_name` | ชื่อ-นามสกุล เจ้าของร้าน |
| | `merchant_email`, `merchant_phone` | อีเมล, เบอร์โทร ร้านค้า |
| | `bank_name`, `bank_account_no` | ข้อมูลธนาคาร |
| | `bank_account_name` | ชื่อบัญชีธนาคาร |
| **API Integration** | `merchant_api_key` | API Key สำหรับ Integration |
| | `merchant_secret_key` | Secret Key สำหรับ Integration |
| **Auto Cancel Withdraw** | `is_auto_cancel_withdraw` | เปิด/ปิด Auto Cancel |
| **Default Callback URL** | `default_callback_url` | URL สำหรับ webhook |
| **MDR Rates** | `deposit_mdr_rate` | อัตราค่าธรรมเนียม Deposit (%) |
| | `withdraw_mdr_rate` | อัตราค่าธรรมเนียม Withdraw (%) |
| **Security** | `password_changed_date` | วันที่เปลี่ยนรหัสผ่านล่าสุด |
| | `last_profile_update` | วันที่อัปเดต Profile ล่าสุด |
| | `profile_updated_by` | ผู้ที่อัปเดต Profile |

### 👤 **USERS Table Features**

| Feature | ฟิลด์ใน users | คำอธิบาย |
|---------|---------------|----------|
| **Google 2FA** | `is_google2fa` | เปิด/ปิด Google Authenticator |
| | `google2fa_secret` | Secret Key สำหรับ 2FA |
| | `google2fa_enabled_date` | วันที่เปิดใช้ 2FA |
| | `google2fa_backup_codes` | Backup codes สำหรับ 2FA |
| | `last_2fa_verify` | วันที่ verify 2FA ล่าสุด |

## 🗄️ **ตารางเสริมใหม่**

### 1. **merchant_security_logs** - Audit Trail สำหรับ Merchant
```sql
CREATE TABLE `merchant_security_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action_type` enum('pin_change','password_change','profile_update','api_key_generate','callback_url_change','auto_cancel_setting','bank_info_update') NOT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `is_success` tinyint(1) DEFAULT 1,
  `failure_reason` varchar(255) DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  ...
);
```

### 2. **merchant_api_keys** - จัดการ API Keys แยกต่างหาก
```sql
CREATE TABLE `merchant_api_keys` (
  `api_key_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `api_key` varchar(255) NOT NULL UNIQUE,
  `secret_key` varchar(255) NOT NULL,
  `key_name` varchar(100) DEFAULT 'Default API Key',
  `permissions` json DEFAULT NULL,
  `ip_whitelist` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `expires_at` timestamp NULL DEFAULT NULL,
  `last_used` timestamp NULL DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  ...
);
```

## 🔧 **Stored Procedures**

### 1. **sp_generate_merchant_api_key** - สร้าง API Key
```sql
CALL sp_generate_merchant_api_key(
    1,                    -- merchant_id
    1,                    -- user_id (ผู้สร้าง)
    'Tiger API Key',      -- key_name
    '{"read": true, "write": true}', -- permissions
    '*************',     -- ip_whitelist
    365,                 -- expires_days
    @api_key,           -- OUT api_key
    @secret_key         -- OUT secret_key
);
```

### 2. **sp_set_merchant_pin_code** - ตั้งค่า PIN Code
```sql
CALL sp_set_merchant_pin_code(
    1,                   -- merchant_id
    1,                   -- user_id
    'encrypted_pin',     -- encrypted_pin
    '*************',    -- ip_address
    'Mozilla/5.0...',   -- user_agent
    @result             -- OUT result
);
```

### 3. **sp_update_merchant_profile** - อัปเดต Profile
```sql
CALL sp_update_merchant_profile(
    1,                   -- merchant_id
    1,                   -- user_id
    'Tiger',            -- first_name
    'Restaurant',       -- last_name
    '<EMAIL>',  -- merchant_email
    '**********',       -- merchant_phone
    'Kasikorn Bank',    -- bank_name
    '**********',       -- bank_account_no
    'Tiger Restaurant', -- bank_account_name
    '*************',   -- ip_address
    @result            -- OUT result
);
```

### 4. **sp_set_auto_cancel_withdraw** - ตั้งค่า Auto Cancel
```sql
CALL sp_set_auto_cancel_withdraw(
    1,                   -- merchant_id
    1,                   -- user_id
    1,                   -- is_auto_cancel (1=เปิด, 0=ปิด)
    '*************',    -- ip_address
    @result             -- OUT result
);
```

### 5. **sp_set_default_callback_url** - ตั้งค่า Callback URL
```sql
CALL sp_set_default_callback_url(
    1,                   -- merchant_id
    1,                   -- user_id
    'https://tiger.com/webhook', -- callback_url
    '*************',    -- ip_address
    @result             -- OUT result
);
```

## 📊 **Views สำหรับการใช้งาน**

### 1. **v_merchant_profile_summary** - ข้อมูล Profile ครบถ้วน
```sql
SELECT 
    merchant_code,
    full_name,
    merchant_email,
    merchant_phone,
    bank_name,
    bank_account_no,
    merchant_api_key,
    merchant_secret_key,
    default_callback_url,
    is_auto_cancel_withdraw,
    deposit_mdr_rate,
    withdraw_mdr_rate,
    is_google2fa,
    pin_code_changed_date,
    password_changed_date
FROM v_merchant_profile_summary 
WHERE merchant_code = 'tiger-001';
```

### 2. **v_merchant_security_summary** - สรุปความปลอดภัย
```sql
SELECT 
    merchant_code,
    security_events_24h,
    security_events_30d,
    last_security_event,
    active_api_keys,
    last_api_usage,
    is_google2fa,
    login_attempts,
    is_locked
FROM v_merchant_security_summary 
WHERE merchant_code = 'tiger-001';
```

## 🚀 **การติดตั้ง**

### 1. **รันไฟล์ SQL**
```bash
mysql -u root -p sugarpay_db < doc/profile_features_table_mapping.sql
```

### 2. **ตรวจสอบการติดตั้ง**
```sql
-- ตรวจสอบฟิลด์ใหม่ใน merchants
DESCRIBE merchants;

-- ตรวจสอบตารางใหม่
SHOW TABLES LIKE 'merchant_%';

-- ตรวจสอบ Stored Procedures
SHOW PROCEDURE STATUS WHERE Db = 'sugarpay_db' AND Name LIKE 'sp_%merchant%';

-- ตรวจสอบ Views
SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_sugarpay_db LIKE 'v_merchant_%';
```

## 💡 **ตัวอย่างการใช้งานจริง**

### 1. **ดูข้อมูล Profile ปัจจุบัน**
```sql
SELECT * FROM v_merchant_profile_summary WHERE merchant_code = 'tiger-001';
```

### 2. **สร้าง API Key ใหม่**
```sql
CALL sp_generate_merchant_api_key(1, 1, 'New API Key', '{}', NULL, 365, @api, @secret);
SELECT @api as api_key, @secret as secret_key;
```

### 3. **ตั้งค่า PIN Code**
```sql
-- เข้ารหัส PIN ด้วย CryptoJS ในฝั่ง Frontend ก่อน
CALL sp_set_merchant_pin_code(1, 1, 'encrypted_pin_here', '*************', 'browser', @result);
```

### 4. **อัปเดต Profile**
```sql
CALL sp_update_merchant_profile(
    1, 1, 'Tiger', 'Restaurant', '<EMAIL>', '**********',
    'Bangkok Bank', '**********', 'Tiger Restaurant Account', '*************', @result
);
```

### 5. **เปิด Auto Cancel Withdraw**
```sql
CALL sp_set_auto_cancel_withdraw(1, 1, 1, '*************', @result);
```

### 6. **ตั้งค่า Callback URL**
```sql
CALL sp_set_default_callback_url(1, 1, 'https://tiger.com/webhook', '*************', @result);
```

### 7. **ดูประวัติการเปลี่ยนแปลง**
```sql
SELECT 
    action_type,
    old_values,
    new_values,
    ip_address,
    created_date
FROM merchant_security_logs 
WHERE merchant_id = 1 
ORDER BY created_date DESC 
LIMIT 10;
```

## 🔐 **ความปลอดภัย**

### 1. **Audit Trail**
- บันทึกการเปลี่ยนแปลงทั้งหมดใน `merchant_security_logs`
- เก็บค่าเก่าและค่าใหม่ในรูปแบบ JSON
- ติดตาม IP Address และ User Agent

### 2. **API Key Management**
- สร้าง API Key แบบสุ่มและไม่ซ้ำ
- รองรับการหมดอายุ
- ติดตามการใช้งาน
- IP Whitelist

### 3. **PIN Code Security**
- เข้ารหัสด้วย CryptoJS AES
- บันทึกวันที่เปลี่ยนแปลง
- Audit trail ครบถ้วน

## 📋 **สรุปการแมป**

| หน้า Profile Feature | Table | ฟิลด์หลัก |
|---------------------|-------|-----------|
| **Withdraw Pin Code** | `merchants` | `pin_code`, `pin_code_changed_date` |
| **Profile** | `merchants` | `first_name`, `last_name`, `merchant_email`, `merchant_phone`, `bank_*` |
| **Auto Cancel Withdraw** | `merchants` | `is_auto_cancel_withdraw` |
| **Default Callback URL** | `merchants` | `default_callback_url` |
| **API Keys** | `merchants` + `merchant_api_keys` | `merchant_api_key`, `merchant_secret_key` |
| **2FA Google Authenticator** | `users` | `is_google2fa`, `google2fa_*` |

**ระบบจะรองรับทุกฟีเจอร์ในหน้า Profile ตามที่ต้องการ!** 🎉
