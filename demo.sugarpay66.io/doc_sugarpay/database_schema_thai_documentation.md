# เอกสารโครงสร้างฐานข้อมูล SugarPay

## ภาพรวมของระบบ

ระบบ SugarPay เป็นระบบการชำระเงินที่ครอบคลุมการจัดการยอดเงินของร้านค้า การเชื่อมต่อกับธนาคาร และการคำนวณค่าธรรมเนียม โดยรองรับการทำงานแบบ Double Entry Accounting และมีระบบ Backoffice ที่สมบูรณ์

## โครงสร้างตารางหลัก

### 1. ตาราง `agents` - ข้อมูลเอเจนต์/ผู้ให้บริการ

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `agent_id` | INT(11) AUTO_INCREMENT | รหัสเอเจนต์ (Primary Key) |
| `agent_code` | VARCHAR(50) UNIQUE | รหัสเอเจนต์ (ไม่ซ้ำ) |
| `agent_name` | VARCHAR(255) | ชื่อเอเจนต์ |
| `contact_person` | VARCHAR(255) | ชื่อผู้ติดต่อ |
| `phone` | VARCHAR(20) | เบอร์โทรศัพท์ |
| `email` | VARCHAR(255) | อีเมล |
| `address` | TEXT | ที่อยู่ |
| `status` | ENUM('active','inactive','suspended') | สถานะการใช้งาน |
| `is_delete` | TINYINT(1) | สถานะการลบ (0=ไม่ลบ, 1=ลบ) |
| `created_date` | TIMESTAMP | วันที่สร้าง |
| `updated_date` | TIMESTAMP | วันที่แก้ไขล่าสุด |

**วัตถุประสงค์**: เก็บข้อมูลของเอเจนต์ที่ให้บริการระบบ SugarPay

### 2. ตาราง `merchants` - ข้อมูลร้านค้า

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `merchant_id` | INT(11) AUTO_INCREMENT | รหัสร้านค้า (Primary Key) |
| `agent_id` | INT(11) | รหัสเอเจนต์ (Foreign Key) |
| `merchant_code` | VARCHAR(50) UNIQUE | รหัสร้านค้า (ไม่ซ้ำ) |
| `merchant_name` | VARCHAR(255) | ชื่อร้านค้า |
| `business_type` | VARCHAR(100) | ประเภทธุรกิจ |
| `contact_person` | VARCHAR(255) | ชื่อผู้ติดต่อ |
| `phone` | VARCHAR(20) | เบอร์โทรศัพท์ |
| `email` | VARCHAR(255) | อีเมล |
| `pin_code` | VARCHAR(255) | รหัส PIN สำหรับยืนยันตัวตน |
| `minimum_2fa` | DECIMAL(15,2) | จำนวนเงินขั้นต่ำที่ต้องใช้ 2FA |
| `api_endpoint` | VARCHAR(500) | URL สำหรับ API ของร้านค้า |
| `api_key` | VARCHAR(255) | API Key |
| `bearer_token` | VARCHAR(500) | Bearer Token สำหรับ Authorization |
| `x_api_key` | VARCHAR(255) | X-API-Key Header |
| `ip_whitelist` | TEXT | รายการ IP ที่อนุญาต (คั่นด้วย comma) |
| `webhook_url` | VARCHAR(500) | URL สำหรับรับ callback |
| `status` | ENUM('active','inactive','suspended') | สถานะการใช้งาน |

**วัตถุประสงค์**: เก็บข้อมูลร้านค้าและการตั้งค่า API Integration

### 3. ตาราง `merchant_balances` - ยอดเงินของร้านค้า

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `balance_id` | INT(11) AUTO_INCREMENT | รหัสยอดเงิน (Primary Key) |
| `merchant_id` | INT(11) UNIQUE | รหัสร้านค้า (Foreign Key, ไม่ซ้ำ) |
| `deposit_balance` | DECIMAL(15,2) | ยอดเงินฝาก (จากการรับชำระ) |
| `withdraw_balance` | DECIMAL(15,2) | ยอดเงินถอน (พร้อมถอนได้) |
| `frozen_balance` | DECIMAL(15,2) | ยอดเงินที่ถูกระงับ |
| `wait_confirm_amount` | DECIMAL(15,2) | ยอดเงินรอการยืนยัน |
| `last_updated` | TIMESTAMP | วันที่อัปเดตล่าสุด |
| `updated_by` | INT(11) | ผู้ที่อัปเดต |

**วัตถุประสงค์**: เก็บยอดเงิน 4 ประเภทของร้านค้า ตามความต้องการที่ระบุ

### 4. ตาราง `bank_accounts` - ข้อมูลบัญชีธนาคาร

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `bank_account_id` | INT(11) AUTO_INCREMENT | รหัสบัญชีธนาคาร (Primary Key) |
| `agent_id` | INT(11) | รหัสเอเจนต์ (Foreign Key) |
| `bank_type` | ENUM('DEPOSIT','WITHDRAW','SAVINGS') | ประเภทบัญชี (3 ประเภท) |
| `bank_name` | VARCHAR(100) | ชื่อธนาคาร |
| `bank_code` | VARCHAR(10) | รหัสธนาคาร |
| `bank_acc_no` | VARCHAR(50) | เลขที่บัญชี |
| `bank_acc_name` | VARCHAR(255) | ชื่อบัญชี |
| `bank_token` | VARCHAR(255) | Token สำหรับเชื่อมต่อ API ธนาคาร |
| `bank_promtpay_no` | VARCHAR(20) | เลขพร้อมเพย์ |
| `priority` | INT(2) | ลำดับความสำคัญ (1-10) |
| `balance` | DECIMAL(15,2) | ยอดเงินในบัญชี |
| `is_enable` | TINYINT(1) | สถานะการใช้งาน |
| `is_primary_withdraw_bank` | TINYINT(1) | บัญชีหลักสำหรับถอนเงิน |
| `is_share_bank_account` | TINYINT(1) | บัญชีใช้ร่วม |

**วัตถุประสงค์**: เก็บข้อมูลบัญชีธนาคาร รองรับ 3 ประเภท แต่ละประเภทได้ 10 บัญชี

### 5. ตาราง `bank_api_configs` - การตั้งค่า Bank API

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `api_config_id` | INT(11) AUTO_INCREMENT | รหัสการตั้งค่า API (Primary Key) |
| `bank_account_id` | INT(11) | รหัสบัญชีธนาคาร (Foreign Key) |
| `api_endpoint` | VARCHAR(500) | URL ของ API ธนาคาร |
| `api_key` | VARCHAR(255) | API Key |
| `api_secret` | VARCHAR(255) | API Secret |
| `bearer_token` | VARCHAR(500) | Bearer Token |
| `x_api_key` | VARCHAR(255) | X-API-Key Header |
| `timeout_seconds` | INT(3) | Timeout ในหน่วยวินาที |
| `retry_attempts` | INT(2) | จำนวนครั้งที่ลองใหม่ |
| `ip_whitelist` | TEXT | รายการ IP ที่อนุญาต |

**วัตถุประสงค์**: เก็บการตั้งค่าสำหรับเชื่อมต่อ API ของแต่ละธนาคาร

### 6. ตาราง `mdr_fee_configs` - การตั้งค่าค่าธรรมเนียม MDR

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `fee_config_id` | INT(11) AUTO_INCREMENT | รหัสการตั้งค่าค่าธรรมเนียม (Primary Key) |
| `agent_id` | INT(11) | รหัสเอเจนต์ (NULL = ทั่วไป) |
| `merchant_id` | INT(11) | รหัสร้านค้า (NULL = ทั่วไป) |
| `transaction_type` | ENUM | ประเภทธุรกรรม (deposit, withdraw, topup, transfer, settlement) |
| `fee_type` | ENUM('percentage','fixed') | ประเภทค่าธรรมเนียม (เปอร์เซ็นต์/คงที่) |
| `fee_value` | DECIMAL(8,4) | ค่าธรรมเนียม |
| `min_fee` | DECIMAL(10,2) | ค่าธรรมเนียมขั้นต่ำ |
| `max_fee` | DECIMAL(10,2) | ค่าธรรมเนียมสูงสุด |
| `effective_from` | TIMESTAMP | วันที่เริ่มมีผล |
| `effective_to` | TIMESTAMP | วันที่สิ้นสุด |
| `is_active` | TINYINT(1) | สถานะการใช้งาน |

**วัตถุประสงค์**: เก็บการตั้งค่าค่าธรรมเนียม MDR ตามโครงสร้างที่กำหนด

## โครงสร้างค่าธรรมเนียม MDR

### ค่าธรรมเนียมมาตรฐาน

| ประเภทรายการ | ค่าธรรมเนียม | หมายเหตุ |
|-------------|-------------|----------|
| **Deposit** | 1.5% | ขาฝาก - หักจากยอดที่ได้รับ |
| **Withdraw** | 10 บาท ต่อรายการ | ขาถอน - **ไม่หักจากยอดถอน** แต่เรียกเก็บแยก |
| **TopUp** | 1.5% | เติมเงินเข้า Withdraw Balance โดยตรง |
| **Transfer** | ฟรี (0 บาท) | โยกเงินจาก Deposit ไป Withdraw Balance |
| **Settlement** | 10 บาท ต่อรายการ | ย้ายเงินจาก Withdraw ไปบัญชีร้านค้า - เรียกเก็บแยก |

### การคำนวณค่าธรรมเนียม

```sql
-- ตัวอย่างการคำนวณ Deposit Fee (1.5%)
-- ยอดที่ลูกค้าจ่าย: 1,000 บาท
-- ค่าธรรมเนียม: 1,000 × 1.5% = 15 บาท
-- ยอดที่ร้านค้าได้รับ: 1,000 - 15 = 985 บาท

-- ตัวอย่างการคำนวณ Withdraw Fee (10 บาท)
-- ยอดที่ร้านค้าถอน: 5,000 บาท
-- ค่าธรรมเนียม: 10 บาท (เรียกเก็บแยก)
-- ยอดที่ร้านค้าได้รับ: 5,000 บาท (เต็มจำนวน)
-- ค่าธรรมเนียมหักจากยอดอื่น หรือเรียกเก็บแยก
```

## ตารางธุรกรรมและการบัญชี

### 7. ตาราง `transactions` - รายการธุรกรรม

| ฟิลด์สำคัญ | ประเภทข้อมูล | คำอธิบาย |
|-----------|-------------|----------|
| `transaction_id` | INT(11) AUTO_INCREMENT | รหัสธุรกรรม (Primary Key) |
| `merchant_id` | INT(11) | รหัสร้านค้า (Foreign Key) |
| `txn_hash` | VARCHAR(100) UNIQUE | STM Ref ID (ไม่ซ้ำ) |
| `order_id` | VARCHAR(100) | รหัสคำสั่งซื้อ |
| `transaction_type` | ENUM | ประเภทธุรกรรม (deposit, withdraw, topup, transfer, settlement) |
| `txn_amount` | DECIMAL(15,2) | จำนวนเงินธุรกรรม |
| `mdr_amount` | DECIMAL(10,2) | ค่าธรรมเนียม MDR |
| `withdraw_fee_amount` | DECIMAL(10,2) | ค่าธรรมเนียมถอนเงิน |
| `net_amount` | DECIMAL(15,2) | จำนวนเงินสุทธิ (คำนวณอัตโนมัติ) |
| `txn_status` | ENUM | สถานะธุรกรรม (PENDING, SUCCESS, FAILED, etc.) |
| `txn_date` | TIMESTAMP | วันที่ทำธุรกรรม |
| `settlement_date` | DATE | วันที่เซ็ตเทิลเมนต์ |

**วัตถุประสงค์**: เก็บรายการธุรกรรมทั้งหมด 5 ประเภท พร้อมข้อมูลค่าธรรมเนียม

### 8. ตาราง `balance_logs` - ประวัติการเปลี่ยนแปลงยอดเงิน

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `log_id` | INT(11) AUTO_INCREMENT | รหัสบันทึก (Primary Key) |
| `merchant_id` | INT(11) | รหัสร้านค้า (Foreign Key) |
| `transaction_id` | INT(11) | รหัสธุรกรรม (Foreign Key) |
| `balance_type` | ENUM | ประเภทยอดเงิน (deposit, withdraw, frozen, wait_confirm) |
| `operation` | ENUM | การดำเนินการ (credit, debit, freeze, unfreeze, transfer_in, transfer_out) |
| `amount_before` | DECIMAL(15,2) | ยอดเงินก่อนเปลี่ยนแปลง |
| `amount_change` | DECIMAL(15,2) | จำนวนเงินที่เปลี่ยนแปลง |
| `amount_after` | DECIMAL(15,2) | ยอดเงินหลังเปลี่ยนแปลง |
| `description` | VARCHAR(500) | คำอธิบาย |

**วัตถุประสงค์**: เก็บประวัติการเปลี่ยนแปลงยอดเงินทุกครั้ง (Audit Trail)

### 9. ตาราง `double_entry_ledger` - บัญชีแยกประเภท

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `ledger_id` | INT(11) AUTO_INCREMENT | รหัสบัญชี (Primary Key) |
| `transaction_id` | INT(11) | รหัสธุรกรรม (Foreign Key) |
| `account_type` | ENUM | ประเภทบัญชี (asset, liability, equity, revenue, expense) |
| `account_name` | VARCHAR(100) | ชื่อบัญชี (เช่น Cash, Accounts_Receivable, Fee_Revenue) |
| `debit_amount` | DECIMAL(15,2) | จำนวนเงินฝั่งเดบิต |
| `credit_amount` | DECIMAL(15,2) | จำนวนเงินฝั่งเครดิต |
| `balance_type` | ENUM | ประเภทยอดเงิน (deposit, withdraw, frozen, wait_confirm, fee, revenue) |
| `description` | VARCHAR(500) | คำอธิบาย |

**วัตถุประสงค์**: รองรับระบบบัญชีแบบ Double Entry Accounting

## ตารางสำหรับ Backoffice

### 10. ตาราง `users` - ผู้ใช้งานระบบ

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `user_id` | INT(11) AUTO_INCREMENT | รหัสผู้ใช้ (Primary Key) |
| `username` | VARCHAR(100) UNIQUE | ชื่อผู้ใช้ (ไม่ซ้ำ) |
| `password` | VARCHAR(255) | รหัสผ่าน (เข้ารหัส) |
| `name` | VARCHAR(255) | ชื่อ-นามสกุล |
| `user_type` | ENUM('admin','agent','merchant','staff') | ประเภทผู้ใช้ |
| `is_google2fa` | TINYINT(1) | เปิดใช้ Google 2FA |
| `google2fa_secret` | VARCHAR(255) | Secret Key สำหรับ 2FA |
| `last_login` | TIMESTAMP | เข้าสู่ระบบครั้งล่าสุด |
| `login_attempts` | INT(2) | จำนวนครั้งที่พยายามเข้าสู่ระบบ |
| `is_locked` | TINYINT(1) | สถานะการล็อกบัญชี |

**วัตถุประสงค์**: เก็บข้อมูลผู้ใช้งานระบบ Backoffice

### 11. ตาราง `user_groups` - กลุ่มผู้ใช้งาน

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `group_id` | INT(11) AUTO_INCREMENT | รหัสกลุ่ม (Primary Key) |
| `group_name_tha` | VARCHAR(255) | ชื่อกลุ่ม (ภาษาไทย) |
| `group_name_eng` | VARCHAR(255) | ชื่อกลุ่ม (ภาษาอังกฤษ) |
| `permissions` | JSON | สิทธิ์การใช้งาน (รูปแบบ JSON) |
| `is_enable` | TINYINT(1) | สถานะการใช้งาน |

**วัตถุประสงค์**: จัดการกลุ่มผู้ใช้และสิทธิ์การเข้าถึง

### 12. ตาราง `admin_logs` - บันทึกการทำงานของ Admin

| ฟิลด์ | ประเภทข้อมูล | คำอธิบาย |
|------|-------------|----------|
| `log_id` | INT(11) AUTO_INCREMENT | รหัสบันทึก (Primary Key) |
| `user_id` | INT(11) | รหัสผู้ใช้ (Foreign Key) |
| `action` | VARCHAR(100) | การกระทำ |
| `target_type` | VARCHAR(50) | ประเภทเป้าหมาย (merchant, transaction, user, etc.) |
| `target_id` | INT(11) | รหัสเป้าหมาย |
| `old_values` | JSON | ค่าเดิม (รูปแบบ JSON) |
| `new_values` | JSON | ค่าใหม่ (รูปแบบ JSON) |
| `ip_address` | VARCHAR(45) | IP Address |
| `user_agent` | TEXT | User Agent |

**วัตถุประสงค์**: บันทึกการทำงานของ Admin เพื่อการตรวจสอบ (Audit Trail)

## Views สำหรับการใช้งาน

### 1. `v_merchant_balance_summary` - สรุปยอดเงินร้านค้า

```sql
SELECT
    m.merchant_code,      -- รหัสร้านค้า
    m.merchant_name,      -- ชื่อร้านค้า
    a.agent_name,         -- ชื่อเอเจนต์
    mb.deposit_balance,   -- ยอดเงินฝาก
    mb.withdraw_balance,  -- ยอดเงินถอน
    mb.frozen_balance,    -- ยอดเงินระงับ
    mb.wait_confirm_amount, -- ยอดรอยืนยัน
    (mb.deposit_balance + mb.withdraw_balance + mb.frozen_balance + mb.wait_confirm_amount) AS total_balance -- ยอดรวม
FROM merchants m
JOIN agents a ON m.agent_id = a.agent_id
LEFT JOIN merchant_balances mb ON m.merchant_id = mb.merchant_id;
```

### 2. `v_transaction_details` - รายละเอียดธุรกรรม

```sql
SELECT
    t.txn_hash,           -- STM Ref ID
    t.order_id,           -- รหัสคำสั่งซื้อ
    t.transaction_type,   -- ประเภทธุรกรรม
    t.txn_amount,         -- จำนวนเงิน
    t.mdr_amount,         -- ค่าธรรมเนียม MDR
    t.withdraw_fee_amount, -- ค่าธรรมเนียมถอน
    t.net_amount,         -- จำนวนสุทธิ
    t.txn_status,         -- สถานะ
    m.merchant_name,      -- ชื่อร้านค้า
    ba.bank_name,         -- ชื่อธนาคาร
    t.txn_date           -- วันที่ทำธุรกรรม
FROM transactions t
JOIN merchants m ON t.merchant_id = m.merchant_id
LEFT JOIN bank_accounts ba ON t.bank_account_id = ba.bank_account_id;
```

### 3. `v_backoffice_dashboard` - Dashboard Backoffice

```sql
SELECT
    (SELECT COUNT(*) FROM merchants WHERE status = 'active') as active_merchants,
    (SELECT COUNT(*) FROM transactions WHERE DATE(created_date) = CURDATE()) as today_transactions,
    (SELECT COALESCE(SUM(txn_amount), 0) FROM transactions WHERE DATE(created_date) = CURDATE() AND txn_status = 'SUCCESS') as today_volume,
    (SELECT COALESCE(SUM(mdr_amount + withdraw_fee_amount), 0) FROM transactions WHERE DATE(created_date) = CURDATE() AND txn_status = 'SUCCESS') as today_revenue;
```

## Stored Procedures สำคัญ

### 1. `sp_update_merchant_balance` - อัปเดตยอดเงินร้านค้า

```sql
CALL sp_update_merchant_balance(
    1,                    -- merchant_id (รหัสร้านค้า)
    123,                  -- transaction_id (รหัสธุรกรรม)
    'deposit',            -- balance_type (ประเภทยอดเงิน)
    'credit',             -- operation (การดำเนินการ)
    1000.00,             -- amount (จำนวนเงิน)
    'Deposit from customer', -- description (คำอธิบาย)
    1                     -- created_by (ผู้สร้าง)
);
```

**การทำงาน**:
1. ดึงยอดเงินปัจจุบัน
2. คำนวณยอดเงินใหม่
3. อัปเดตยอดเงินในตาราง merchant_balances
4. บันทึกประวัติใน balance_logs

### 2. `sp_create_notification` - สร้างการแจ้งเตือน

```sql
CALL sp_create_notification(
    1,                    -- user_id (รหัสผู้ใช้)
    NULL,                 -- agent_id (รหัสเอเจนต์)
    NULL,                 -- merchant_id (รหัสร้านค้า)
    'warning',            -- notification_type (ประเภทการแจ้งเตือน)
    'ธุรกรรมผิดปกติ',      -- title (หัวข้อ)
    'พบธุรกรรมที่ผิดปกติ กรุณาตรวจสอบ', -- message (ข้อความ)
    '/transactions/123',  -- action_url (URL สำหรับดำเนินการ)
    'high'               -- priority (ระดับความสำคัญ)
);
```

## ตัวอย่างการใช้งานจริง

### 1. การสร้างธุรกรรม Deposit

```sql
-- 1. สร้างธุรกรรม
INSERT INTO transactions (
    merchant_id, agent_id, bank_account_id, txn_hash, order_id,
    transaction_type, txn_amount, mdr_amount, txn_status
) VALUES (
    1, 1, 1, 'TXN001_1234567890', 'ORDER_001',
    'deposit', 1000.00, 15.00, 'SUCCESS'
);

-- 2. อัปเดตยอดเงิน deposit_balance
CALL sp_update_merchant_balance(
    1, LAST_INSERT_ID(), 'deposit', 'credit', 985.00,
    'Deposit after MDR deduction', 1
);

-- 3. บันทึก Double Entry
INSERT INTO double_entry_ledger (transaction_id, merchant_id, account_type, account_name, debit_amount, credit_amount) VALUES
(LAST_INSERT_ID(), 1, 'asset', 'Deposit_Balance', 985.00, 0.00),
(LAST_INSERT_ID(), 1, 'revenue', 'MDR_Fee_Revenue', 15.00, 0.00),
(LAST_INSERT_ID(), 1, 'asset', 'Cash_Received', 0.00, 1000.00);
```

### 2. การโอนเงินจาก Deposit ไป Withdraw (Transfer)

```sql
-- 1. สร้างธุรกรรม Transfer (ฟรี)
INSERT INTO transactions (
    merchant_id, agent_id, txn_hash, order_id,
    transaction_type, txn_amount, mdr_amount, txn_status
) VALUES (
    1, 1, 'TXN002_1234567891', 'TRANSFER_001',
    'transfer', 2000.00, 0.00, 'SUCCESS'
);

-- 2. หักยอด deposit_balance
CALL sp_update_merchant_balance(
    1, LAST_INSERT_ID(), 'deposit', 'transfer_out', 2000.00,
    'Transfer to withdraw balance', 1
);

-- 3. เพิ่มยอด withdraw_balance
CALL sp_update_merchant_balance(
    1, LAST_INSERT_ID(), 'withdraw', 'transfer_in', 2000.00,
    'Transfer from deposit balance', 1
);
```

### 3. การถอนเงิน (Withdraw)

```sql
-- 1. สร้างธุรกรรม Withdraw (ค่าธรรมเนียม 10 บาท ไม่หักจากยอดถอน)
INSERT INTO transactions (
    merchant_id, agent_id, bank_account_id, txn_hash, order_id,
    transaction_type, txn_amount, withdraw_fee_amount, txn_status,
    txn_bank_name, txn_acc_no, txn_acc_name
) VALUES (
    1, 1, 4, 'TXN003_1234567892', 'WITHDRAW_001',
    'withdraw', 5000.00, 10.00, 'SUCCESS',
    'Kasikorn Bank', '**********', 'Tiger Restaurant Account'
);

-- 2. หักยอด withdraw_balance (เต็มจำนวน)
CALL sp_update_merchant_balance(
    1, LAST_INSERT_ID(), 'withdraw', 'debit', 5000.00,
    'Withdraw to bank account', 1
);

-- หมายเหตุ: ค่าธรรมเนียม 10 บาท จะเรียกเก็บแยกจากยอดอื่น
```

## การตรวจสอบความถูกต้อง

### 1. ตรวจสอบยอดเงิน

```sql
-- ตรวจสอบยอดเงินของร้านค้า
SELECT
    merchant_code,
    deposit_balance,
    withdraw_balance,
    frozen_balance,
    wait_confirm_amount,
    (deposit_balance + withdraw_balance + frozen_balance + wait_confirm_amount) as total_balance
FROM v_merchant_balance_summary
WHERE merchant_code = 'tiger-001';
```

### 2. ตรวจสอบ Double Entry Balance

```sql
-- ตรวจสอบความสมดุลของ Double Entry
SELECT
    transaction_id,
    SUM(debit_amount) as total_debit,
    SUM(credit_amount) as total_credit,
    (SUM(debit_amount) - SUM(credit_amount)) as balance_check
FROM double_entry_ledger
GROUP BY transaction_id
HAVING balance_check != 0;  -- ไม่ควรมีผลลัพธ์ (ต้องสมดุล)
```

### 3. ตรวจสอบธุรกรรมที่ล้มเหลว

```sql
-- ดูธุรกรรมที่ล้มเหลววันนี้
SELECT
    COUNT(*) as failed_count,
    SUM(txn_amount) as failed_amount,
    transaction_type
FROM transactions
WHERE txn_status = 'FAILED'
  AND DATE(created_date) = CURDATE()
GROUP BY transaction_type;
```

## การบำรุงรักษาระบบ

### 1. งานประจำวัน

```sql
-- ตรวจสอบธุรกรรมค้างอนุมัติ
SELECT COUNT(*) as pending_approvals
FROM transaction_approvals
WHERE approval_status = 'pending';

-- ตรวจสอบการกระทบยอดธนาคาร
SELECT
    ba.bank_name,
    br.system_balance,
    br.bank_statement_balance,
    br.difference_amount,
    br.reconciliation_status
FROM bank_reconciliation br
JOIN bank_accounts ba ON br.bank_account_id = ba.bank_account_id
WHERE br.reconciliation_date = CURDATE()
  AND br.reconciliation_status != 'matched';
```

### 2. งานประจำสัปดาห์

```sql
-- สรุปรายได้จากค่าธรรมเนียม 7 วันที่ผ่านมา
SELECT
    DATE(txn_date) as revenue_date,
    SUM(mdr_amount) as mdr_revenue,
    SUM(withdraw_fee_amount) as withdraw_fee_revenue,
    SUM(mdr_amount + withdraw_fee_amount) as total_revenue,
    COUNT(*) as transaction_count
FROM transactions
WHERE txn_status = 'SUCCESS'
  AND txn_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(txn_date)
ORDER BY revenue_date DESC;
```

## สรุป

เอกสารนี้ครอบคลุมโครงสร้างฐานข้อมูล SugarPay ที่:

✅ **รองรับยอดเงิน 4 ประเภท**: Deposit, Withdraw, Frozen, Wait_Confirm
✅ **รองรับ Bank API**: 3 ประเภท, 10 บัญชีต่อประเภท
✅ **รองรับ MDR Fees**: ตามโครงสร้างที่กำหนด
✅ **รองรับ Double Entry**: บัญชีแยกประเภทมาตรฐาน
✅ **รองรับ Backoffice**: ระบบจัดการครบถ้วน
✅ **รองรับ API Integration**: ทั้งร้านค้าและธนาคาร

ระบบพร้อมใช้งานและสามารถขยายเพิ่มเติมได้ตามความต้องการในอนาคต
