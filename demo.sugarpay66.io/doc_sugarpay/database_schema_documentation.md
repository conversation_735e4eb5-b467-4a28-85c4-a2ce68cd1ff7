# SugarPay Database Schema Documentation

## ภาพรวมของระบบ

Database schema นี้ออกแบบมาเพื่อรองรับระบบ SugarPay ที่มีความต้องการดังนี้:

### ✅ Merchant Balance (4 ประเภท)
- **Deposit Balance**: ยอดเงินฝาก (จากการรับชำระ)
- **Withdraw Balance**: ยอดเงินถอน (พร้อมถอนได้)
- **Frozen Balance**: ยอดเงินที่ถูกระงับ
- **Wait Confirm Amount**: ยอดรอการยืนยัน

### ✅ Bank API Integration
- รองรับ 3 ประเภท: **DEPOSIT**, **WITHDRAW**, **SAVINGS**
- แต่ละประเภทสามารถมีได้ถึง **10 บัญชี**
- เชื่อมโยงกับ API ธนาคาร: Authorization Bearer, x-api-key, api_endpoint, ip_whitelist

### ✅ Merchant API Integration
- เชื่อมโยงกับเว็บลูกค้า: Authorization Bearer, x-api-key, api_endpoint, ip_whitelist
- รองรับ webhook_url สำหรับ callback

### ✅ MDR Fee Structure (ตามที่กำหนด)
| ประเภทรายการ | ค่าธรรมเนียม MDR | หมายเหตุ |
|-------------|-----------------|----------|
| **Deposit** | 1.5% | ขาฝาก |
| **Withdraw** | 10 THB ต่อรายการ | ไม่หักจากยอดถอน แต่เรียกเก็บแยก |
| **TopUp** | 1.5% | เติมเงินเข้า Withdraw Balance |
| **Transfer** | ฟรี (0 THB) | โยกเงินภายใน |
| **Settlement** | 10 THB ต่อรายการ | เรียกเก็บแยก |

### ✅ Double Entry Accounting
- ตาราง `double_entry_ledger` สำหรับบันทึกบัญชีแยกประเภท
- รองรับ account types: asset, liability, equity, revenue, expense
- มี debit_amount และ credit_amount แยกกัน

## โครงสร้างตารางหลัก

### 1. **agents** - ข้อมูลเอเจนต์
```sql
agent_id, agent_code, agent_name, status, contact_info...
```

### 2. **merchants** - ข้อมูลร้านค้า
```sql
merchant_id, agent_id, merchant_code, merchant_name, status,
api_endpoint, api_key, bearer_token, x_api_key, ip_whitelist...
```

### 3. **merchant_balances** - ยอดเงิน 4 ประเภท
```sql
merchant_id, deposit_balance, withdraw_balance, 
frozen_balance, wait_confirm_amount...
```

### 4. **bank_accounts** - บัญชีธนาคาร (3 ประเภท, 10 บัญชีต่อประเภท)
```sql
bank_account_id, agent_id, bank_type (DEPOSIT/WITHDRAW/SAVINGS),
bank_name, bank_acc_no, priority, is_enable...
```

### 5. **bank_api_configs** - การตั้งค่า Bank API
```sql
api_config_id, bank_account_id, api_endpoint, api_key, 
bearer_token, x_api_key, ip_whitelist...
```

### 6. **mdr_fee_configs** - การตั้งค่าค่าธรรมเนียม
```sql
fee_config_id, agent_id, merchant_id, transaction_type,
fee_type (percentage/fixed), fee_value, min_fee, max_fee...
```

### 7. **transactions** - รายการธุรกรรม
```sql
transaction_id, merchant_id, txn_hash, order_id, transaction_type,
txn_amount, mdr_amount, withdraw_fee_amount, net_amount,
txn_status, bank_info, customer_info...
```

### 8. **balance_logs** - ประวัติการเปลี่ยนแปลงยอดเงิน
```sql
log_id, merchant_id, transaction_id, balance_type, operation,
amount_before, amount_change, amount_after...
```

### 9. **double_entry_ledger** - บัญชีแยกประเภท
```sql
ledger_id, transaction_id, account_type, account_name,
debit_amount, credit_amount, balance_type...
```

### 10. **fee_transactions** - รายการค่าธรรมเนียม
```sql
fee_transaction_id, transaction_id, fee_type, calculated_fee,
actual_fee, fee_rate, base_amount...
```

## Features เพิ่มเติม

### 🔐 User Management
- **users**: ผู้ใช้งานระบบ (admin, agent, merchant, staff)
- **user_groups**: กลุ่มผู้ใช้งานพร้อม permissions
- **user_group_members**: สมาชิกในกลุ่ม

### 🚫 Security Features
- **blacklist_accounts**: บัญชีที่ถูกบล็อก
- Google 2FA support ใน users table
- IP whitelist ใน API configs

### 📊 Views สำหรับ Reporting
- **v_merchant_balance_summary**: สรุปยอดเงินร้านค้า
- **v_transaction_details**: รายละเอียดธุรกรรมครบถ้วน
- **v_active_mdr_fees**: ค่าธรรมเนียมที่มีผล

### ⚡ Performance Optimization
- Indexes ที่เหมาะสมสำหรับ queries ที่ใช้บ่อย
- Stored procedures สำหรับ operations ที่ซับซ้อน
- Triggers สำหรับ data integrity

## การใช้งาน

### 1. สร้าง Database
```sql
-- รัน script: sugarpay_complete_database_schema.sql
```

### 2. Insert ข้อมูลเริ่มต้น
```sql
-- Default MDR fees จะถูก insert อัตโนมัติ
-- สร้าง agent และ merchant ตามต้องการ
```

### 3. ตัวอย่างการ Query

#### ดูยอดเงินร้านค้า:
```sql
SELECT * FROM v_merchant_balance_summary 
WHERE merchant_code = 'tiger-001';
```

#### ดูธุรกรรมล่าสุด:
```sql
SELECT * FROM v_transaction_details 
WHERE merchant_code = 'tiger-001' 
ORDER BY txn_date DESC LIMIT 10;
```

#### อัพเดทยอดเงิน:
```sql
CALL sp_update_merchant_balance(
    1, 123, 'deposit', 'credit', 1000.00, 
    'Deposit from customer', 1
);
```

## ข้อดีของ Schema นี้

1. **✅ ครบถ้วน**: รองรับทุกความต้องการที่ระบุ
2. **✅ ยืดหยุ่น**: สามารถปรับแต่งค่าธรรมเนียมได้
3. **✅ ปลอดภัย**: มี audit trail และ security features
4. **✅ Performance**: มี indexes และ views ที่เหมาะสม
5. **✅ Scalable**: รองรับการขยายระบบในอนาคต
6. **✅ Double Entry**: รองรับบัญชีแยกประเภทมาตรฐาน

## การบำรุงรักษา

### Backup Strategy
- ทำ backup ข้อมูลทุกวัน
- เก็บ transaction logs อย่างน้อย 7 ปี
- ทำ archive ข้อมูลเก่าเป็นระยะ

### Monitoring
- ตรวจสอบ balance consistency
- Monitor API response times
- Alert เมื่อมี failed transactions

### Security
- เปลี่ยน API keys เป็นระยะ
- Review user permissions
- Monitor suspicious activities
