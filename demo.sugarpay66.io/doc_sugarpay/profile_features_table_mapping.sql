-- =====================================================
-- Profile Features Table Mapping สำหรับ SugarPay
-- แก้ไขตาม requirement: ใช้ merchants table สำหรับส่วนใหญ่, users table เฉพาะ 2FA
-- =====================================================

-- =====================================================
-- 1. แก้ไข MERCHANTS TABLE สำหรับ Profile Features
-- =====================================================

ALTER TABLE `merchants` 
-- Withdraw Pin Code
ADD COLUMN `pin_code` varchar(255) DEFAULT NULL COMMENT 'PIN Code สำหรับถอนเงิน (เข้ารหัส)' AFTER `minimum_2fa`,
ADD COLUMN `pin_code_changed_date` timestamp NULL DEFAULT NULL COMMENT 'วันที่เปลี่ยน PIN Code ล่าสุด' AFTER `pin_code`,

-- Profile Information  
ADD COLUMN `first_name` varchar(255) DEFAULT NULL COMMENT 'ชื่อจริงของเจ้าของร้าน' AFTER `contact_person`,
ADD COLUMN `last_name` varchar(255) DEFAULT NULL COMMENT 'นามสกุลของเจ้าของร้าน' AFTER `first_name`,
ADD COLUMN `merchant_email` varchar(255) DEFAULT NULL COMMENT 'อีเมลของร้านค้า' AFTER `email`,
ADD COLUMN `merchant_phone` varchar(20) DEFAULT NULL COMMENT 'เบอร์โทรของร้านค้า' AFTER `phone`,
ADD COLUMN `bank_name` varchar(100) DEFAULT NULL COMMENT 'ชื่อธนาคารของร้านค้า' AFTER `merchant_phone`,
ADD COLUMN `bank_account_no` varchar(50) DEFAULT NULL COMMENT 'เลขที่บัญชีธนาคารของร้านค้า' AFTER `bank_name`,
ADD COLUMN `bank_account_name` varchar(255) DEFAULT NULL COMMENT 'ชื่อบัญชีธนาคาร' AFTER `bank_account_no`,

-- API Integration (ย้ายจาก users มา merchants)
ADD COLUMN `merchant_api_key` varchar(255) DEFAULT NULL COMMENT 'API Key สำหรับ Integration' AFTER `x_api_key`,
ADD COLUMN `merchant_secret_key` varchar(255) DEFAULT NULL COMMENT 'Secret Key สำหรับ Integration' AFTER `merchant_api_key`,

-- Auto Cancel Withdraw
ADD COLUMN `is_auto_cancel_withdraw` tinyint(1) DEFAULT 0 COMMENT 'เปิด/ปิด Auto Cancel Withdraw เมื่อปิดระบบ' AFTER `webhook_url`,

-- Default Callback URL (เปลี่ยนชื่อให้ชัดเจน)
ADD COLUMN `default_callback_url` varchar(500) DEFAULT NULL COMMENT 'Default Callback URL สำหรับ webhook' AFTER `is_auto_cancel_withdraw`,

-- MDR Rates (เพิ่มเพื่อแสดงในหน้า Profile)
ADD COLUMN `deposit_mdr_rate` decimal(5,2) DEFAULT 1.50 COMMENT 'อัตราค่าธรรมเนียม Deposit (%)' AFTER `default_callback_url`,
ADD COLUMN `withdraw_mdr_rate` decimal(5,2) DEFAULT 0.00 COMMENT 'อัตราค่าธรรมเนียม Withdraw (%)' AFTER `deposit_mdr_rate`,

-- Password Management
ADD COLUMN `password_changed_date` timestamp NULL DEFAULT NULL COMMENT 'วันที่เปลี่ยนรหัสผ่านล่าสุด' AFTER `withdraw_mdr_rate`,

-- Additional Security
ADD COLUMN `last_profile_update` timestamp NULL DEFAULT NULL COMMENT 'วันที่อัปเดต Profile ล่าสุด' AFTER `password_changed_date`,
ADD COLUMN `profile_updated_by` int(11) DEFAULT NULL COMMENT 'ผู้ที่อัปเดต Profile ล่าสุด' AFTER `last_profile_update`;

-- เพิ่ม Indexes สำหรับ Performance
ALTER TABLE `merchants` 
ADD INDEX `idx_merchants_pin_code` (`pin_code`),
ADD INDEX `idx_merchants_api_key` (`merchant_api_key`),
ADD INDEX `idx_merchants_bank_account` (`bank_account_no`),
ADD INDEX `idx_merchants_profile_update` (`last_profile_update`);

-- =====================================================
-- 2. แก้ไข USERS TABLE เฉพาะ 2FA
-- =====================================================

-- เพิ่มฟิลด์ที่เกี่ยวข้องกับ 2FA เท่านั้น
ALTER TABLE `users` 
ADD COLUMN `google2fa_enabled_date` timestamp NULL DEFAULT NULL COMMENT 'วันที่เปิดใช้ Google 2FA' AFTER `google2fa_secret`,
ADD COLUMN `google2fa_backup_codes` json DEFAULT NULL COMMENT 'Backup codes สำหรับ 2FA' AFTER `google2fa_enabled_date`,
ADD COLUMN `last_2fa_verify` timestamp NULL DEFAULT NULL COMMENT 'วันที่ verify 2FA ล่าสุด' AFTER `google2fa_backup_codes`;

-- =====================================================
-- 3. สร้างตาราง merchant_security_logs
-- =====================================================

CREATE TABLE `merchant_security_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT 'ผู้ที่ทำการเปลี่ยนแปลง',
  `action_type` enum('pin_change','password_change','profile_update','api_key_generate','callback_url_change','auto_cancel_setting','bank_info_update') NOT NULL,
  `old_values` json DEFAULT NULL COMMENT 'ค่าเดิมก่อนเปลี่ยนแปลง',
  `new_values` json DEFAULT NULL COMMENT 'ค่าใหม่หลังเปลี่ยนแปลง',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `is_success` tinyint(1) DEFAULT 1,
  `failure_reason` varchar(255) DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`),
  KEY `idx_merchant_security_logs_merchant` (`merchant_id`, `created_date`),
  KEY `idx_merchant_security_logs_action` (`action_type`, `created_date`),
  KEY `idx_merchant_security_logs_user` (`user_id`),
  CONSTRAINT `fk_merchant_security_logs_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`),
  CONSTRAINT `fk_merchant_security_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. สร้างตาราง merchant_api_keys (แยกจาก users)
-- =====================================================

CREATE TABLE `merchant_api_keys` (
  `api_key_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `api_key` varchar(255) NOT NULL UNIQUE,
  `secret_key` varchar(255) NOT NULL,
  `key_name` varchar(100) DEFAULT 'Default API Key',
  `permissions` json DEFAULT NULL COMMENT 'สิทธิ์การใช้งาน API',
  `ip_whitelist` text DEFAULT NULL COMMENT 'IP ที่อนุญาตให้ใช้ API Key นี้',
  `is_active` tinyint(1) DEFAULT 1,
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'วันหมดอายุ',
  `last_used` timestamp NULL DEFAULT NULL COMMENT 'ใช้งานครั้งล่าสุด',
  `usage_count` int(11) DEFAULT 0 COMMENT 'จำนวนครั้งที่ใช้งาน',
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`api_key_id`),
  UNIQUE KEY `uk_merchant_api_key` (`api_key`),
  KEY `idx_merchant_api_keys_merchant` (`merchant_id`),
  KEY `idx_merchant_api_keys_active` (`is_active`, `expires_at`),
  CONSTRAINT `fk_merchant_api_keys_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`),
  CONSTRAINT `fk_merchant_api_keys_user` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. Stored Procedures สำหรับ Merchant Profile Management
-- =====================================================

DELIMITER //

-- Procedure สำหรับสร้าง Merchant API Key
CREATE PROCEDURE `sp_generate_merchant_api_key`(
    IN p_merchant_id INT,
    IN p_user_id INT,
    IN p_key_name VARCHAR(100),
    IN p_permissions JSON,
    IN p_ip_whitelist TEXT,
    IN p_expires_days INT,
    OUT p_api_key VARCHAR(255),
    OUT p_secret_key VARCHAR(255)
)
BEGIN
    DECLARE v_api_key VARCHAR(255);
    DECLARE v_secret_key VARCHAR(255);
    DECLARE v_expires_at TIMESTAMP;
    
    -- สร้าง API Key และ Secret Key
    SET v_api_key = CONCAT(
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8)
    );
    
    SET v_secret_key = CONCAT(
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8)
    );
    
    -- คำนวณวันหมดอายุ
    IF p_expires_days > 0 THEN
        SET v_expires_at = DATE_ADD(NOW(), INTERVAL p_expires_days DAY);
    ELSE
        SET v_expires_at = NULL;
    END IF;
    
    -- ปิดการใช้งาน API Key เก่า (ถ้ามี)
    UPDATE merchant_api_keys SET is_active = 0 WHERE merchant_id = p_merchant_id;
    
    -- เพิ่ม API Key ใหม่
    INSERT INTO merchant_api_keys (
        merchant_id, api_key, secret_key, key_name, 
        permissions, ip_whitelist, expires_at, created_by
    ) VALUES (
        p_merchant_id, v_api_key, v_secret_key, p_key_name,
        p_permissions, p_ip_whitelist, v_expires_at, p_user_id
    );
    
    -- อัปเดต merchants table
    UPDATE merchants SET 
        merchant_api_key = v_api_key,
        merchant_secret_key = v_secret_key,
        last_profile_update = NOW(),
        profile_updated_by = p_user_id,
        updated_date = NOW(),
        updated_by = p_user_id
    WHERE merchant_id = p_merchant_id;
    
    -- บันทึก Security Log
    INSERT INTO merchant_security_logs (
        merchant_id, user_id, action_type, 
        new_values, ip_address, is_success
    ) VALUES (
        p_merchant_id, p_user_id, 'api_key_generate',
        JSON_OBJECT('api_key', v_api_key, 'key_name', p_key_name),
        NULL, 1
    );
    
    -- Return values
    SET p_api_key = v_api_key;
    SET p_secret_key = v_secret_key;
END //

-- Procedure สำหรับตั้งค่า Merchant PIN Code
CREATE PROCEDURE `sp_set_merchant_pin_code`(
    IN p_merchant_id INT,
    IN p_user_id INT,
    IN p_encrypted_pin VARCHAR(255),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT,
    OUT p_result INT
)
BEGIN
    DECLARE v_old_pin VARCHAR(255);
    
    -- ดึง PIN เก่า
    SELECT pin_code INTO v_old_pin FROM merchants WHERE merchant_id = p_merchant_id;
    
    -- อัปเดต PIN Code
    UPDATE merchants SET 
        pin_code = p_encrypted_pin,
        pin_code_changed_date = NOW(),
        last_profile_update = NOW(),
        profile_updated_by = p_user_id,
        updated_date = NOW(),
        updated_by = p_user_id
    WHERE merchant_id = p_merchant_id;
    
    -- บันทึก Security Log
    INSERT INTO merchant_security_logs (
        merchant_id, user_id, action_type, 
        old_values, new_values, ip_address, user_agent, is_success
    ) VALUES (
        p_merchant_id, p_user_id, 'pin_change',
        JSON_OBJECT('had_pin', IF(v_old_pin IS NOT NULL, true, false)),
        JSON_OBJECT('pin_set', true, 'changed_date', NOW()),
        p_ip_address, p_user_agent, 1
    );
    
    SET p_result = 0; -- สำเร็จ
END //

-- Procedure สำหรับอัปเดต Merchant Profile
CREATE PROCEDURE `sp_update_merchant_profile`(
    IN p_merchant_id INT,
    IN p_user_id INT,
    IN p_first_name VARCHAR(255),
    IN p_last_name VARCHAR(255),
    IN p_merchant_email VARCHAR(255),
    IN p_merchant_phone VARCHAR(20),
    IN p_bank_name VARCHAR(100),
    IN p_bank_account_no VARCHAR(50),
    IN p_bank_account_name VARCHAR(255),
    IN p_ip_address VARCHAR(45),
    OUT p_result INT
)
BEGIN
    DECLARE v_old_values JSON;
    
    -- ดึงข้อมูลเก่า
    SELECT JSON_OBJECT(
        'first_name', first_name,
        'last_name', last_name,
        'merchant_email', merchant_email,
        'merchant_phone', merchant_phone,
        'bank_name', bank_name,
        'bank_account_no', bank_account_no,
        'bank_account_name', bank_account_name
    ) INTO v_old_values
    FROM merchants WHERE merchant_id = p_merchant_id;
    
    -- อัปเดตข้อมูล
    UPDATE merchants SET 
        first_name = p_first_name,
        last_name = p_last_name,
        merchant_email = p_merchant_email,
        merchant_phone = p_merchant_phone,
        bank_name = p_bank_name,
        bank_account_no = p_bank_account_no,
        bank_account_name = p_bank_account_name,
        last_profile_update = NOW(),
        profile_updated_by = p_user_id,
        updated_date = NOW(),
        updated_by = p_user_id
    WHERE merchant_id = p_merchant_id;
    
    -- บันทึก Security Log
    INSERT INTO merchant_security_logs (
        merchant_id, user_id, action_type, 
        old_values, new_values, ip_address, is_success
    ) VALUES (
        p_merchant_id, p_user_id, 'profile_update',
        v_old_values,
        JSON_OBJECT(
            'first_name', p_first_name,
            'last_name', p_last_name,
            'merchant_email', p_merchant_email,
            'merchant_phone', p_merchant_phone,
            'bank_name', p_bank_name,
            'bank_account_no', p_bank_account_no,
            'bank_account_name', p_bank_account_name
        ),
        p_ip_address, 1
    );
    
    SET p_result = 0; -- สำเร็จ
END //

-- Procedure สำหรับตั้งค่า Auto Cancel Withdraw
CREATE PROCEDURE `sp_set_auto_cancel_withdraw`(
    IN p_merchant_id INT,
    IN p_user_id INT,
    IN p_is_auto_cancel TINYINT(1),
    IN p_ip_address VARCHAR(45),
    OUT p_result INT
)
BEGIN
    DECLARE v_old_setting TINYINT(1);
    
    -- ดึงการตั้งค่าเก่า
    SELECT is_auto_cancel_withdraw INTO v_old_setting 
    FROM merchants WHERE merchant_id = p_merchant_id;
    
    -- อัปเดตการตั้งค่า
    UPDATE merchants SET 
        is_auto_cancel_withdraw = p_is_auto_cancel,
        last_profile_update = NOW(),
        profile_updated_by = p_user_id,
        updated_date = NOW(),
        updated_by = p_user_id
    WHERE merchant_id = p_merchant_id;
    
    -- บันทึก Security Log
    INSERT INTO merchant_security_logs (
        merchant_id, user_id, action_type, 
        old_values, new_values, ip_address, is_success
    ) VALUES (
        p_merchant_id, p_user_id, 'auto_cancel_setting',
        JSON_OBJECT('is_auto_cancel_withdraw', v_old_setting),
        JSON_OBJECT('is_auto_cancel_withdraw', p_is_auto_cancel),
        p_ip_address, 1
    );
    
    SET p_result = 0; -- สำเร็จ
END //

-- Procedure สำหรับตั้งค่า Default Callback URL
CREATE PROCEDURE `sp_set_default_callback_url`(
    IN p_merchant_id INT,
    IN p_user_id INT,
    IN p_callback_url VARCHAR(500),
    IN p_ip_address VARCHAR(45),
    OUT p_result INT
)
BEGIN
    DECLARE v_old_url VARCHAR(500);
    
    -- ดึง URL เก่า
    SELECT default_callback_url INTO v_old_url 
    FROM merchants WHERE merchant_id = p_merchant_id;
    
    -- อัปเดต URL
    UPDATE merchants SET 
        default_callback_url = p_callback_url,
        last_profile_update = NOW(),
        profile_updated_by = p_user_id,
        updated_date = NOW(),
        updated_by = p_user_id
    WHERE merchant_id = p_merchant_id;
    
    -- บันทึก Security Log
    INSERT INTO merchant_security_logs (
        merchant_id, user_id, action_type, 
        old_values, new_values, ip_address, is_success
    ) VALUES (
        p_merchant_id, p_user_id, 'callback_url_change',
        JSON_OBJECT('default_callback_url', v_old_url),
        JSON_OBJECT('default_callback_url', p_callback_url),
        p_ip_address, 1
    );
    
    SET p_result = 0; -- สำเร็จ
END //

DELIMITER ;

-- =====================================================
-- 6. Views สำหรับ Profile Management
-- =====================================================

-- View สำหรับ Merchant Profile Summary
CREATE VIEW `v_merchant_profile_summary` AS
SELECT
    m.merchant_id,
    m.merchant_code,
    m.merchant_name,
    m.first_name,
    m.last_name,
    CONCAT(COALESCE(m.first_name, ''), ' ', COALESCE(m.last_name, '')) as full_name,
    m.merchant_email,
    m.merchant_phone,
    m.bank_name,
    m.bank_account_no,
    m.bank_account_name,
    m.merchant_api_key,
    m.merchant_secret_key,
    m.default_callback_url,
    m.is_auto_cancel_withdraw,
    m.deposit_mdr_rate,
    m.withdraw_mdr_rate,
    m.pin_code_changed_date,
    m.password_changed_date,
    m.last_profile_update,
    m.status,
    a.agent_name,
    a.agent_code,
    -- User 2FA info
    u.is_google2fa,
    u.google2fa_enabled_date,
    u.last_2fa_verify
FROM merchants m
LEFT JOIN agents a ON m.agent_id = a.agent_id
LEFT JOIN users u ON u.merchant_id = m.merchant_id AND u.user_type = 'merchant'
WHERE m.is_delete = 0;

-- View สำหรับ Merchant Security Summary
CREATE VIEW `v_merchant_security_summary` AS
SELECT
    m.merchant_id,
    m.merchant_code,
    m.merchant_name,
    m.pin_code_changed_date,
    m.password_changed_date,
    m.last_profile_update,
    -- Security events count
    (SELECT COUNT(*) FROM merchant_security_logs WHERE merchant_id = m.merchant_id AND created_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as security_events_24h,
    (SELECT COUNT(*) FROM merchant_security_logs WHERE merchant_id = m.merchant_id AND created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as security_events_30d,
    (SELECT created_date FROM merchant_security_logs WHERE merchant_id = m.merchant_id ORDER BY created_date DESC LIMIT 1) as last_security_event,
    -- API Key info
    (SELECT COUNT(*) FROM merchant_api_keys WHERE merchant_id = m.merchant_id AND is_active = 1) as active_api_keys,
    (SELECT last_used FROM merchant_api_keys WHERE merchant_id = m.merchant_id AND is_active = 1 ORDER BY last_used DESC LIMIT 1) as last_api_usage,
    -- User 2FA status
    u.is_google2fa,
    u.login_attempts,
    u.is_locked
FROM merchants m
LEFT JOIN users u ON u.merchant_id = m.merchant_id AND u.user_type = 'merchant'
WHERE m.is_delete = 0;

-- =====================================================
-- 7. Triggers สำหรับ Auto-logging
-- =====================================================

DELIMITER //

-- Trigger สำหรับ log การเปลี่ยนแปลง merchants
CREATE TRIGGER `tr_merchants_profile_update_log`
AFTER UPDATE ON `merchants`
FOR EACH ROW
BEGIN
    -- ตรวจสอบว่ามีการเปลี่ยนแปลงฟิลด์ที่สำคัญหรือไม่
    IF (OLD.pin_code != NEW.pin_code) OR
       (OLD.first_name != NEW.first_name) OR
       (OLD.last_name != NEW.last_name) OR
       (OLD.merchant_email != NEW.merchant_email) OR
       (OLD.merchant_phone != NEW.merchant_phone) OR
       (OLD.bank_name != NEW.bank_name) OR
       (OLD.bank_account_no != NEW.bank_account_no) OR
       (OLD.default_callback_url != NEW.default_callback_url) OR
       (OLD.is_auto_cancel_withdraw != NEW.is_auto_cancel_withdraw) THEN

        -- บันทึก log อัตโนมัติ (ถ้าไม่ได้บันทึกผ่าน stored procedure)
        IF NEW.profile_updated_by IS NULL THEN
            INSERT INTO merchant_security_logs (
                merchant_id, action_type,
                old_values, new_values, is_success
            ) VALUES (
                NEW.merchant_id, 'profile_update',
                JSON_OBJECT(
                    'pin_code_changed', IF(OLD.pin_code != NEW.pin_code, true, false),
                    'profile_changed', IF(OLD.first_name != NEW.first_name OR OLD.last_name != NEW.last_name, true, false),
                    'contact_changed', IF(OLD.merchant_email != NEW.merchant_email OR OLD.merchant_phone != NEW.merchant_phone, true, false),
                    'bank_changed', IF(OLD.bank_name != NEW.bank_name OR OLD.bank_account_no != NEW.bank_account_no, true, false),
                    'settings_changed', IF(OLD.default_callback_url != NEW.default_callback_url OR OLD.is_auto_cancel_withdraw != NEW.is_auto_cancel_withdraw, true, false)
                ),
                JSON_OBJECT('updated_date', NEW.updated_date),
                1
            );
        END IF;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- 8. Default Data และ Sample Data
-- =====================================================

-- อัปเดตข้อมูล merchants ที่มีอยู่แล้ว
UPDATE merchants SET
    first_name = 'tiger-001',
    last_name = '',
    merchant_email = '',
    merchant_phone = '',
    bank_name = 'WAIT_BANK_ACCOUNT',
    bank_account_no = '**********',
    bank_account_name = 'tiger-001',
    deposit_mdr_rate = 1.50,
    withdraw_mdr_rate = 0.00,
    is_auto_cancel_withdraw = 0
WHERE merchant_code = 'tiger-001';

-- สร้าง API Keys สำหรับ merchants ที่มีอยู่
INSERT INTO merchant_api_keys (merchant_id, api_key, secret_key, key_name)
SELECT
    merchant_id,
    'd79edc5c-e89667cb-1210320f-b0059db0',
    'c7b5f857-f418c9c6-c9f7e4b7-83c691ae',
    'Default API Key'
FROM merchants
WHERE merchant_code = 'tiger-001'
  AND NOT EXISTS (SELECT 1 FROM merchant_api_keys WHERE merchant_id = merchants.merchant_id);

-- อัปเดต merchants table ด้วย API Keys
UPDATE merchants m
JOIN merchant_api_keys mak ON m.merchant_id = mak.merchant_id
SET
    m.merchant_api_key = mak.api_key,
    m.merchant_secret_key = mak.secret_key
WHERE mak.is_active = 1;

-- =====================================================
-- 9. Indexes เพิ่มเติมสำหรับ Performance
-- =====================================================

-- Indexes สำหรับ merchant_security_logs
CREATE INDEX `idx_merchant_security_logs_comprehensive` ON `merchant_security_logs` (`merchant_id`, `action_type`, `created_date`);
CREATE INDEX `idx_merchant_security_logs_date_range` ON `merchant_security_logs` (`created_date`, `is_success`);

-- Indexes สำหรับ merchant_api_keys
CREATE INDEX `idx_merchant_api_keys_comprehensive` ON `merchant_api_keys` (`merchant_id`, `is_active`, `expires_at`);
CREATE INDEX `idx_merchant_api_keys_usage` ON `merchant_api_keys` (`last_used`, `usage_count`);

-- =====================================================
-- 10. Admin Settings เพิ่มเติม
-- =====================================================

INSERT INTO admin_settings (setting_key, setting_value, setting_type, description) VALUES
('merchant_pin_required', 'true', 'boolean', 'บังคับใช้ PIN Code สำหรับ merchant'),
('merchant_api_key_expires_days', '365', 'number', 'จำนวนวันที่ Merchant API Key หมดอายุ'),
('auto_cancel_withdraw_default', 'false', 'boolean', 'ค่าเริ่มต้นของ Auto Cancel Withdraw'),
('require_callback_url', 'false', 'boolean', 'บังคับใส่ Callback URL'),
('merchant_profile_audit_retention_days', '365', 'number', 'เก็บ audit log ของ merchant profile กี่วัน')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
