# SugarPay Database Documentation

## ภาพรวม

โฟลเดอร์นี้ประกอบด้วยเอกสารและไฟล์ SQL ที่ครบถ้วนสำหรับระบบ SugarPay ซึ่งเป็นระบบการชำระเงินที่รองรับ:

- ✅ **Merchant Balance 4 ประเภท**: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Wait_Confirm
- ✅ **Bank API Integration**: 3 ประเภท (DEPOSIT/WITHDRAW/SAVINGS), 10 บัญชีต่อประเภท
- ✅ **MDR Fee Structure**: ตามโครงสร้างที่กำหนด (1.5% deposit, 10 THB withdraw, etc.)
- ✅ **Double Entry Accounting**: ระบบบัญชีแยกประเภทมาตรฐาน
- ✅ **Backoffice System**: ระบบจัดการครบถ้วน

---

## 📁 รายการไฟล์

### 🗄️ **ไฟล์ SQL Database**

#### 1. `sugarpay_complete_database_schema.sql` ⭐ **หลัก**
- **คำอธิบาย**: ไฟล์ Schema หลักของระบบ SugarPay
- **เนื้อหา**: 
  - ตาราง 15 ตาราง (agents, merchants, transactions, etc.)
  - Views 6 views สำหรับ reporting
  - Stored procedures 1 ตัว
  - Triggers สำหรับ data integrity
  - Default MDR fee configurations
- **การใช้งาน**: รันไฟล์นี้ก่อนเป็นอันดับแรก

#### 2. `backoffice_enhancement.sql` 🏢 **Backoffice**
- **คำอธิบาย**: ส่วนเสริมสำหรับ Backoffice Management
- **เนื้อหา**:
  - ตาราง admin_settings, admin_logs, notifications
  - ระบบ approval workflow
  - ระบบ monitoring และ reconciliation
  - Views สำหรับ backoffice dashboard
  - Stored procedures เพิ่มเติม
- **การใช้งาน**: รันหลังจาก schema หลักแล้ว

#### 3. `sample_data_and_usage.sql` 📊 **ข้อมูลตัวอย่าง**
- **คำอธิบาย**: ข้อมูลตัวอย่างและ queries สำหรับทดสอบ
- **เนื้อหา**:
  - Sample agents, merchants, bank accounts
  - ธุรกรรมตัวอย่างทุกประเภท
  - Queries สำหรับ reporting
  - การทดสอบ double entry
- **การใช้งาน**: รันเพื่อทดสอบระบบ (ไม่จำเป็นใน production)

---

### 📚 **เอกสารภาษาอังกฤษ**

#### 4. `database_schema_documentation.md` 📖 **เอกสารหลัก**
- **คำอธิบาย**: เอกสารโครงสร้างฐานข้อมูลฉบับสมบูรณ์
- **เนื้อหา**:
  - ภาพรวมของระบบ
  - โครงสร้างตารางและความสัมพันธ์
  - MDR fee structure
  - ตัวอย่างการใช้งาน
  - Performance considerations

#### 5. `implementation_guide.md` 🛠️ **คู่มือการพัฒนา**
- **คำอธิบาย**: คู่มือการ implement ระบบ
- **เนื้อหา**:
  - ขั้นตอนการติดตั้ง
  - Transaction flow
  - Security implementation
  - API integration examples
  - Performance optimization

#### 6. `backoffice_features_summary.md` 🏢 **สรุป Backoffice**
- **คำอธิบาย**: สรุป features ของ Backoffice
- **เนื้อหา**:
  - Core backoffice features
  - Advanced features
  - Dashboard views
  - Operations และ maintenance

---

### 📚 **เอกสารภาษาไทย**

#### 7. `database_schema_thai_documentation.md` 📖 **เอกสารหลัก (ไทย)**
- **คำอธิบาย**: เอกสารโครงสร้างฐานข้อมูลภาษาไทยฉบับสมบูรณ์
- **เนื้อหา**:
  - อธิบายทุกตารางพร้อมฟิลด์และวัตถุประสงค์
  - โครงสร้างค่าธรรมเนียม MDR แบบละเอียด
  - ตัวอย่าง Views และ Stored Procedures
  - ตัวอย่างการใช้งานจริง
  - การตรวจสอบความถูกต้อง

#### 8. `installation_guide_thai.md` 🛠️ **คู่มือติดตั้ง (ไทย)**
- **คำอธิบาย**: คู่มือการติดตั้งแบบละเอียดภาษาไทย
- **เนื้อหา**:
  - ขั้นตอนการติดตั้งทีละขั้น
  - การตั้งค่าเริ่มต้น
  - การทดสอบระบบ
  - การตั้งค่าความปลอดภัย
  - การสำรองข้อมูล
  - การแก้ไขปัญหา

---

## 🚀 **ขั้นตอนการติดตั้ง**

### 1. **เตรียมความพร้อม**
```bash
# สร้างฐานข้อมูล
mysql -u root -p
CREATE DATABASE sugarpay_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sugarpay_db;
```

### 2. **รันไฟล์ SQL ตามลำดับ**
```bash
# 1. รัน Schema หลัก
mysql -u root -p sugarpay_db < doc/sugarpay_complete_database_schema.sql

# 2. รัน Backoffice Enhancement
mysql -u root -p sugarpay_db < doc/backoffice_enhancement.sql

# 3. รัน Sample Data (ถ้าต้องการ)
mysql -u root -p sugarpay_db < doc/sample_data_and_usage.sql
```

### 3. **ตรวจสอบการติดตั้ง**
```sql
-- ตรวจสอบตารางที่สร้าง
SHOW TABLES;

-- ตรวจสอบ Views
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- ตรวจสอบ Default MDR Fees
SELECT * FROM mdr_fee_configs WHERE is_active = 1;
```

---

## 📊 **โครงสร้างข้อมูลหลัก**

### **Merchant Balance (4 ประเภท)**
| ประเภท | คำอธิบาย |
|--------|----------|
| `deposit_balance` | ยอดเงินฝาก (จากการรับชำระ) |
| `withdraw_balance` | ยอดเงินถอน (พร้อมถอนได้) |
| `frozen_balance` | ยอดเงินที่ถูกระงับ |
| `wait_confirm_amount` | ยอดรอการยืนยัน |

### **MDR Fee Structure**
| ประเภทรายการ | ค่าธรรมเนียม | หมายเหตุ |
|-------------|-------------|----------|
| **Deposit** | 1.5% | ขาฝาก |
| **Withdraw** | 10 THB ต่อรายการ | ไม่หักจากยอดถอน |
| **TopUp** | 1.5% | เติมเงินเข้า Withdraw Balance |
| **Transfer** | ฟรี (0 THB) | โยกเงินภายใน |
| **Settlement** | 10 THB ต่อรายการ | เรียกเก็บแยก |

### **Bank Account Types**
| ประเภท | จำนวนสูงสุด | วัตถุประสงค์ |
|--------|-------------|-------------|
| `DEPOSIT` | 10 บัญชี | รับเงินจากลูกค้า |
| `WITHDRAW` | 10 บัญชี | จ่ายเงินให้ร้านค้า |
| `SAVINGS` | 10 บัญชี | เก็บเงินสำรอง |

---

## 🔧 **การใช้งานเบื้องต้น**

### **ดูยอดเงินร้านค้า**
```sql
SELECT * FROM v_merchant_balance_summary WHERE merchant_code = 'tiger-001';
```

### **ดูธุรกรรมล่าสุด**
```sql
SELECT * FROM v_transaction_details ORDER BY txn_date DESC LIMIT 10;
```

### **อัปเดตยอดเงิน**
```sql
CALL sp_update_merchant_balance(1, 123, 'deposit', 'credit', 1000.00, 'Deposit from customer', 1);
```

### **ดู Dashboard**
```sql
SELECT * FROM v_backoffice_dashboard;
```

---

## 📞 **การสนับสนุน**

หากมีคำถามหรือต้องการความช่วยเหลือ:

1. **อ่านเอกสาร**: เริ่มจาก `database_schema_thai_documentation.md`
2. **ดูตัวอย่าง**: ใน `sample_data_and_usage.sql`
3. **ติดตั้งระบบ**: ตาม `installation_guide_thai.md`
4. **พัฒนาระบบ**: ตาม `implementation_guide.md`

---

## 📝 **หมายเหตุ**

- ✅ ระบบรองรับ **Double Entry Accounting**
- ✅ มี **Audit Trail** ครบถ้วน
- ✅ รองรับ **Multi-bank Integration**
- ✅ มี **Backoffice** สมบูรณ์
- ✅ **Security Features** ครบครัน
- ✅ **Performance Optimized**

**ระบบพร้อมใช้งาน Production!** 🎉
