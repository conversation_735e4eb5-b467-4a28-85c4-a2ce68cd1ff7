# คู่มือการติดตั้งระบบ SugarPay

## ขั้นตอนการติดตั้ง

### 1. เตรียมความพร้อม

#### ความต้องการของระบบ
- **Database**: MySQL 8.0+ หรือ MariaDB 10.5+
- **PHP**: 8.0+ (สำหรับ Backend API)
- **Web Server**: Apache 2.4+ หรือ Nginx 1.18+
- **Memory**: อย่างน้อย 4GB RAM
- **Storage**: อย่างน้อย 50GB สำหรับข้อมูล

#### เครื่องมือที่ต้องใช้
- MySQL Client หรือ phpMyAdmin
- Text Editor หรือ IDE
- Terminal/Command Prompt

### 2. สร้างฐานข้อมูล

#### 2.1 เข้าสู่ MySQL
```bash
mysql -u root -p
```

#### 2.2 สร้าง Database
```sql
-- สร้างฐานข้อมูล
CREATE DATABASE sugarpay_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- สร้างผู้ใช้สำหรับระบบ (แนะนำ)
CREATE USER 'sugarpay_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON sugarpay_db.* TO 'sugarpay_user'@'localhost';
FLUSH PRIVILEGES;

-- เลือกใช้ฐานข้อมูล
USE sugarpay_db;
```

#### 2.3 รันไฟล์ Schema
```bash
# รันไฟล์ Schema หลัก
mysql -u sugarpay_user -p sugarpay_db < sugarpay_complete_database_schema.sql

# รันไฟล์ Backoffice Enhancement
mysql -u sugarpay_user -p sugarpay_db < backoffice_enhancement.sql

# รันไฟล์ Sample Data (ถ้าต้องการ)
mysql -u sugarpay_user -p sugarpay_db < sample_data_and_usage.sql
```

### 3. ตรวจสอบการติดตั้ง

#### 3.1 ตรวจสอบตารางที่สร้าง
```sql
-- ดูรายการตารางทั้งหมด
SHOW TABLES;

-- ผลลัพธ์ที่ควรได้ (15 ตาราง)
/*
+---------------------------+
| Tables_in_sugarpay_db     |
+---------------------------+
| admin_logs                |
| admin_settings            |
| agents                    |
| balance_logs              |
| bank_account_mdr_rates    |
| bank_accounts             |
| bank_api_configs          |
| bank_reconciliation       |
| blacklist_accounts        |
| double_entry_ledger       |
| fee_transactions          |
| mdr_fee_configs           |
| merchant_balances         |
| merchant_limits           |
| merchants                 |
| notifications             |
| report_schedules          |
| system_maintenance        |
| transaction_approvals     |
| transactions              |
| user_group_members        |
| user_groups               |
| users                     |
+---------------------------+
*/
```

#### 3.2 ตรวจสอบ Views
```sql
-- ดู Views ที่สร้าง
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- ผลลัพธ์ที่ควรได้
/*
+----------------------------------+------------+
| Tables_in_sugarpay_db            | Table_type |
+----------------------------------+------------+
| v_active_mdr_fees               | VIEW       |
| v_backoffice_dashboard          | VIEW       |
| v_merchant_balance_summary      | VIEW       |
| v_transaction_details           | VIEW       |
| v_transaction_monitoring        | VIEW       |
| v_user_activity_monitoring      | VIEW       |
+----------------------------------+------------+
*/
```

#### 3.3 ตรวจสอบ Stored Procedures
```sql
-- ดู Stored Procedures
SHOW PROCEDURE STATUS WHERE Db = 'sugarpay_db';

-- ผลลัพธ์ที่ควรได้
/*
+-------------+---------------------------+
| Db          | Name                      |
+-------------+---------------------------+
| sugarpay_db | sp_create_notification    |
| sugarpay_db | sp_log_admin_action       |
| sugarpay_db | sp_update_merchant_balance|
+-------------+---------------------------+
*/
```

#### 3.4 ตรวจสอบ Default Data
```sql
-- ตรวจสอบ MDR Fee Configs เริ่มต้น
SELECT transaction_type, fee_type, fee_value, is_active 
FROM mdr_fee_configs 
WHERE agent_id IS NULL AND merchant_id IS NULL;

-- ผลลัพธ์ที่ควรได้
/*
+------------------+----------+-----------+-----------+
| transaction_type | fee_type | fee_value | is_active |
+------------------+----------+-----------+-----------+
| deposit          | percentage| 1.5000   | 1         |
| withdraw         | fixed    | 10.0000   | 1         |
| topup            | percentage| 1.5000   | 1         |
| transfer         | fixed    | 0.0000    | 1         |
| settlement       | fixed    | 10.0000   | 1         |
+------------------+----------+-----------+-----------+
*/

-- ตรวจสอบ Admin Settings เริ่มต้น
SELECT setting_key, setting_value, setting_type 
FROM admin_settings 
ORDER BY setting_key;
```

### 4. การตั้งค่าเริ่มต้น

#### 4.1 สร้าง Agent แรก
```sql
INSERT INTO agents (agent_code, agent_name, contact_person, phone, email, status) 
VALUES ('AGENT001', 'SugarPay Agent 1', 'Admin User', '0812345678', '<EMAIL>', 'active');

SET @agent_id = LAST_INSERT_ID();
```

#### 4.2 สร้าง Admin User แรก
```sql
INSERT INTO users (agent_id, username, password, name, email, user_type, is_google2fa) 
VALUES (@agent_id, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', '<EMAIL>', 'admin', 1);

-- หมายเหตุ: password ข้างต้นคือ 'password' (ควรเปลี่ยนในการใช้งานจริง)
```

#### 4.3 สร้าง User Group สำหรับ Admin
```sql
INSERT INTO user_groups (agent_id, group_name_tha, group_name_eng, description, permissions) 
VALUES (@agent_id, 'ผู้ดูแลระบบ', 'System Administrator', 'Full system access', 
'{"dashboard": true, "users": true, "merchants": true, "transactions": true, "reports": true, "settings": true}');

-- เพิ่ม Admin เข้ากลุ่ม
INSERT INTO user_group_members (group_id, user_id) 
VALUES (LAST_INSERT_ID(), (SELECT user_id FROM users WHERE username = 'admin'));
```

#### 4.4 สร้าง Merchant ตัวอย่าง
```sql
INSERT INTO merchants (agent_id, merchant_code, merchant_name, business_type, contact_person, phone, email, status) 
VALUES (@agent_id, 'DEMO001', 'ร้านทดสอบ Demo', 'Restaurant', 'เจ้าของร้าน', '**********', '<EMAIL>', 'active');

-- ระบบจะสร้าง merchant_balances อัตโนมัติผ่าน Trigger
```

#### 4.5 สร้าง Bank Account ตัวอย่าง
```sql
-- บัญชีสำหรับรับเงิน (DEPOSIT)
INSERT INTO bank_accounts (agent_id, bank_type, bank_name, bank_code, bank_acc_no, bank_acc_name, priority, is_enable) 
VALUES (@agent_id, 'DEPOSIT', 'ธนาคารกสิกรไทย', 'KBANK', '**********', 'SugarPay Deposit Account', 1, 1);

-- บัญชีสำหรับจ่ายเงิน (WITHDRAW)
INSERT INTO bank_accounts (agent_id, bank_type, bank_name, bank_code, bank_acc_no, bank_acc_name, priority, is_enable, is_primary_withdraw_bank) 
VALUES (@agent_id, 'WITHDRAW', 'ธนาคารกสิกรไทย', 'KBANK', '**********', 'SugarPay Withdraw Account', 1, 1, 1);

-- บัญชีออมทรัพย์ (SAVINGS)
INSERT INTO bank_accounts (agent_id, bank_type, bank_name, bank_code, bank_acc_no, bank_acc_name, priority, is_enable) 
VALUES (@agent_id, 'SAVINGS', 'ธนาคารกสิกรไทย', 'KBANK', '**********', 'SugarPay Savings Account', 1, 1);
```

### 5. ทดสอบระบบ

#### 5.1 ทดสอบการสร้างธุรกรรม
```sql
-- ทดสอบ Deposit Transaction
INSERT INTO transactions (merchant_id, agent_id, bank_account_id, txn_hash, order_id, transaction_type, txn_amount, mdr_amount, txn_status, txn_date) 
VALUES (1, @agent_id, 1, 'TEST001_' || UNIX_TIMESTAMP(), 'ORDER_TEST_001', 'deposit', 1000.00, 15.00, 'SUCCESS', NOW());

-- อัปเดตยอดเงิน
CALL sp_update_merchant_balance(1, LAST_INSERT_ID(), 'deposit', 'credit', 985.00, 'Test deposit transaction', 1);
```

#### 5.2 ทดสอบ Views
```sql
-- ทดสอบ Dashboard View
SELECT * FROM v_backoffice_dashboard;

-- ทดสอบ Merchant Balance View
SELECT * FROM v_merchant_balance_summary WHERE merchant_code = 'DEMO001';

-- ทดสอบ Transaction Details View
SELECT * FROM v_transaction_details ORDER BY transaction_id DESC LIMIT 5;
```

#### 5.3 ทดสอบ Stored Procedures
```sql
-- ทดสอบการสร้าง Notification
CALL sp_create_notification(1, @agent_id, NULL, 'info', 'ทดสอบระบบ', 'การติดตั้งเสร็จสมบูรณ์', NULL, 'normal');

-- ตรวจสอบ Notification ที่สร้าง
SELECT * FROM notifications WHERE title = 'ทดสอบระบบ';
```

### 6. การตั้งค่าความปลอดภัย

#### 6.1 เปลี่ยนรหัสผ่าน Default
```sql
-- เปลี่ยนรหัสผ่าน Admin (ใช้ password ที่เข้ารหัสแล้ว)
UPDATE users SET password = '$2y$10$your_new_hashed_password' WHERE username = 'admin';
```

#### 6.2 ตั้งค่า IP Whitelist
```sql
-- อัปเดต IP Whitelist สำหรับ Merchant
UPDATE merchants SET ip_whitelist = '*************,***********' WHERE merchant_code = 'DEMO001';

-- อัปเดต IP Whitelist สำหรับ Bank API
UPDATE bank_api_configs SET ip_whitelist = '***********/24,10.0.0.0/8' WHERE api_config_id = 1;
```

#### 6.3 ตั้งค่า System Settings
```sql
-- ตั้งค่าจำนวนครั้งการ Login ที่อนุญาต
UPDATE admin_settings SET setting_value = '3' WHERE setting_key = 'max_login_attempts';

-- ตั้งค่า Session Timeout
UPDATE admin_settings SET setting_value = '60' WHERE setting_key = 'session_timeout_minutes';

-- ตั้งค่าจำนวนเงินที่ต้องอนุมัติ
UPDATE admin_settings SET setting_value = '100000' WHERE setting_key = 'transaction_approval_required_amount';
```

### 7. การสำรองข้อมูล

#### 7.1 สร้าง Backup Script
```bash
#!/bin/bash
# backup_sugarpay.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/sugarpay"
DB_NAME="sugarpay_db"
DB_USER="sugarpay_user"
DB_PASS="your_secure_password"

# สร้างโฟลเดอร์ backup
mkdir -p $BACKUP_DIR

# สำรองฐานข้อมูล
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/sugarpay_backup_$DATE.sql

# บีบอัดไฟล์
gzip $BACKUP_DIR/sugarpay_backup_$DATE.sql

# ลบไฟล์เก่าที่เก็บไว้เกิน 30 วัน
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: sugarpay_backup_$DATE.sql.gz"
```

#### 7.2 ตั้งค่า Cron Job สำหรับ Backup อัตโนมัติ
```bash
# เปิดไฟล์ crontab
crontab -e

# เพิ่มบรรทัดนี้เพื่อสำรองข้อมูลทุกวันเวลา 02:00
0 2 * * * /path/to/backup_sugarpay.sh
```

### 8. การตรวจสอบ Performance

#### 8.1 ตรวจสอบ Indexes
```sql
-- ดู Indexes ที่สร้าง
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'sugarpay_db' 
ORDER BY TABLE_NAME, INDEX_NAME;
```

#### 8.2 ตรวจสอบ Query Performance
```sql
-- เปิดใช้ Slow Query Log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- ตรวจสอบ Query ที่ช้า
SHOW VARIABLES LIKE 'slow_query_log%';
```

### 9. การ Monitor ระบบ

#### 9.1 ตรวจสอบสถานะระบบ
```sql
-- ตรวจสอบการเชื่อมต่อฐานข้อมูล
SELECT 
    COUNT(*) as total_connections,
    SUM(CASE WHEN COMMAND != 'Sleep' THEN 1 ELSE 0 END) as active_connections
FROM INFORMATION_SCHEMA.PROCESSLIST;

-- ตรวจสอบขนาดฐานข้อมูล
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'sugarpay_db'
GROUP BY table_schema;
```

#### 9.2 ตั้งค่า Monitoring Queries
```sql
-- สร้าง View สำหรับ System Health
CREATE VIEW v_system_health AS
SELECT 
    (SELECT COUNT(*) FROM transactions WHERE txn_status = 'PENDING' AND created_date < DATE_SUB(NOW(), INTERVAL 30 MINUTE)) as stuck_transactions,
    (SELECT COUNT(*) FROM users WHERE is_locked = 1) as locked_users,
    (SELECT COUNT(*) FROM notifications WHERE is_read = 0 AND notification_type = 'error') as unread_errors,
    (SELECT COUNT(*) FROM transaction_approvals WHERE approval_status = 'pending' AND requested_date < DATE_SUB(NOW(), INTERVAL 24 HOUR)) as overdue_approvals;
```

### 10. การแก้ไขปัญหาเบื้องต้น

#### 10.1 ปัญหาการเชื่อมต่อฐานข้อมูล
```sql
-- ตรวจสอบสิทธิ์ผู้ใช้
SHOW GRANTS FOR 'sugarpay_user'@'localhost';

-- ตรวจสอบการเชื่อมต่อ
SELECT USER(), DATABASE(), CONNECTION_ID();
```

#### 10.2 ปัญหายอดเงินไม่ตรงกัน
```sql
-- ตรวจสอบความสอดคล้องของยอดเงิน
SELECT 
    m.merchant_code,
    mb.deposit_balance,
    (SELECT COALESCE(SUM(CASE WHEN operation IN ('credit', 'transfer_in') THEN amount_change ELSE -amount_change END), 0) 
     FROM balance_logs 
     WHERE merchant_id = m.merchant_id AND balance_type = 'deposit') as calculated_deposit
FROM merchants m
JOIN merchant_balances mb ON m.merchant_id = mb.merchant_id
HAVING mb.deposit_balance != calculated_deposit;
```

#### 10.3 ปัญหา Double Entry ไม่สมดุล
```sql
-- ตรวจสอบ Double Entry Balance
SELECT 
    transaction_id,
    SUM(debit_amount) as total_debit,
    SUM(credit_amount) as total_credit,
    ABS(SUM(debit_amount) - SUM(credit_amount)) as imbalance
FROM double_entry_ledger
GROUP BY transaction_id
HAVING imbalance > 0.01;  -- อนุญาตความผิดพลาด 0.01 บาท
```

## สรุป

การติดตั้งระบบ SugarPay เสร็จสมบูรณ์แล้ว! ระบบพร้อมใช้งานด้วยฟีเจอร์:

✅ **ระบบจัดการยอดเงิน 4 ประเภท**  
✅ **ระบบ Bank API Integration**  
✅ **ระบบคำนวณค่าธรรมเนียม MDR**  
✅ **ระบบ Double Entry Accounting**  
✅ **ระบบ Backoffice ครบถ้วน**  
✅ **ระบบความปลอดภัยและการตรวจสอบ**  

### ขั้นตอนถัดไป:
1. พัฒนา Frontend UI
2. สร้าง API Endpoints
3. ทดสอบการเชื่อมต่อกับธนาคาร
4. Deploy ไปยัง Production Environment
