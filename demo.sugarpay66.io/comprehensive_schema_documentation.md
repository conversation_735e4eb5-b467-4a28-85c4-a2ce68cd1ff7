# SugarPay Comprehensive Database Schema Documentation

## ภาพรวมของ Database Schema

Schema นี้ออกแบบมาเพื่อรองรับระบบ SugarPay ที่มีการจัดการยอดเงินของร้านค้า (Merchant Balance), การเชื่อมต่อกับ Bank API และการคำนวณค่าธรรมเนียม MDR โดยอิงจากโครงสร้างข้อมูลที่มีอยู่ในระบบปัจจุบัน

## ตารางหลักและความสัมพันธ์

### 1. **agents** - ข้อมูลเอเจนต์/ผู้ให้บริการ
- เก็บข้อมูลของเอเจนต์ที่ให้บริการระบบ SugarPay
- มี agent_code เป็น unique identifier
- รองรับการมีหลายเอเจนต์ในระบบ

### 2. **merchants** - ข้อมูลร้านค้า
- เก็บข้อมูลพื้นฐานของร้านค้าภายใต้แต่ละเอเจนต์
- มี merchant_code เป็น unique identifier
- รองรับ pin_code และ minimum_2fa สำหรับความปลอดภัย
- สถานะการใช้งาน (active, inactive, suspended)

### 3. **merchant_balances** - ยอดเงินของร้านค้า
- **deposit_balance**: ยอดเงินฝาก (จากการรับชำระ)
- **withdraw_balance**: ยอดเงินถอน (พร้อมถอนได้)
- **frozen_balance**: ยอดเงินที่ถูกระงับ
- **wait_confirm_amount**: ยอดรอการยืนยัน
- เชื่อมโยงกับ merchants ผ่าน merchant_id

### 4. **bank_accounts** - ข้อมูลบัญชีธนาคาร
- รองรับ 3 ประเภท: DEPOSIT, WITHDRAW, SAVINGS
- แต่ละประเภทสามารถมีได้ถึง 10 บัญชี
- มี priority สำหรับการเลือกใช้บัญชี
- รองรับการตั้งค่าต่างๆ เช่น is_enable, is_primary_withdraw_bank
- เก็บข้อมูล bank_token สำหรับการเชื่อมต่อ API

### 5. **bank_api_configs** - การตั้งค่า Bank API
- เก็บการตั้งค่า API endpoint, key, secret
- รองรับ timeout และ retry configuration
- เชื่อมโยงกับ bank_accounts

### 6. **mdr_fee_configs** - การตั้งค่าค่าธรรมเนียม MDR
- รองรับทั้ง percentage และ fixed fee
- สามารถตั้งค่าเฉพาะ agent หรือ merchant ได้
- มี effective_from และ effective_to สำหรับการจัดการเวลา
- รองรับการตั้งค่า min_fee และ max_fee

### 7. **bank_account_mdr_rates** - อัตราค่าธรรมเนียมของแต่ละบัญชี
- เก็บอัตราค่าธรรมเนียมเฉพาะของแต่ละบัญชีธนาคาร
- รองรับ QR Code, Transfer, TopUp, Movement rates

### 8. **transactions** - รายการธุรกรรม
- เก็บรายการธุรกรรมทั้งหมด 5 ประเภท:
  - **deposit**: ฝากเงิน (รับชำระจากลูกค้า)
  - **withdraw**: ถอนเงิน (ถอนไปบัญชีส่วนตัว)
  - **topup**: เติมเงินเข้า withdraw balance โดยตรง
  - **transfer**: โยกเงินจาก deposit ไป withdraw (ฟรี)
  - **settlement**: ย้ายเงินจาก withdraw ไปบัญชีร้านค้าอัตโนมัติ
- มี txn_hash เป็น STM Ref ID
- รองรับ order_id, instruction_ref_no
- แยกค่าธรรมเนียม mdr_amount และ withdraw_fee_amount

### 9. **balance_logs** - ประวัติการเปลี่ยนแปลงยอดเงิน
- เก็บ audit trail ของการเปลี่ยนแปลงยอดเงินทุกครั้ง
- มี amount_before, amount_change, amount_after
- รองรับ operation: credit, debit, freeze, unfreeze, transfer_in, transfer_out

### 10. **fee_transactions** - รายการค่าธรรมเนียม
- เก็บรายละเอียดการคำนวณค่าธรรมเนียม
- มี calculated_fee (ที่คำนวณได้) และ actual_fee (ที่เรียกเก็บจริง)
- เชื่อมโยงกับ mdr_fee_configs

## การจัดการค่าธรรมเนียม MDR

### Default Fee Structure:
- **Deposit**: 1.5% (ขาฝาก)
- **Withdraw**: 10 THB ต่อรายการ (ไม่หักจากยอดถอน)
- **TopUp**: 1.5% (เติมเงินเข้า Withdraw Balance)
- **Transfer**: ฟรี (0 THB) (โยกเงินภายใน)
- **Settlement**: 10 THB ต่อรายการ (เรียกเก็บแยก)

### Fee Calculation Logic:
1. ระบบจะหา fee config ที่เหมาะสม (merchant-specific > agent-specific > global)
2. คำนวณค่าธรรมเนียมตาม fee_type (percentage หรือ fixed)
3. ตรวจสอบ min_fee และ max_fee
4. บันทึกใน fee_transactions

## ข้อดีของ Schema นี้

1. **Scalability**: รองรับการเพิ่ม Bank API ได้ไม่จำกัด (10 APIs ต่อประเภท)
2. **Flexibility**: สามารถปรับค่าธรรมเนียมได้ตามเวลาและเฉพาะ merchant
3. **Audit Trail**: มีการเก็บประวัติการเปลี่ยนแปลงครบถ้วน
4. **Performance**: มี Index ที่เหมาะสมสำหรับ query ที่ใช้บ่อย
5. **Data Integrity**: มี Foreign Key constraints ป้องกันข้อมูลผิดพลาด
6. **Real-world Compatibility**: ออกแบบตามโครงสร้างข้อมูลที่มีอยู่จริง

## การใช้งานตัวอย่าง

### Query ยอดเงินของร้านค้า:
```sql
SELECT m.merchant_name, mb.deposit_balance, mb.withdraw_balance, mb.frozen_balance
FROM merchants m
JOIN merchant_balances mb ON m.merchant_id = mb.merchant_id
WHERE m.merchant_code = 'tiger-001';
```

### Query ประวัติธุรกรรม:
```sql
SELECT t.txn_hash, t.transaction_type, t.txn_amount, t.mdr_amount, t.txn_status
FROM transactions t
WHERE t.merchant_id = 1
ORDER BY t.txn_date DESC;
```

### Query Bank APIs ที่ใช้งานได้:
```sql
SELECT ba.bank_name, ba.bank_type, ba.priority, bac.api_endpoint
FROM bank_accounts ba
LEFT JOIN bank_api_configs bac ON ba.agent_bank_id = bac.agent_bank_id
WHERE ba.agent_id = 701 AND ba.is_enable = 1 AND ba.is_delete = 0
ORDER BY ba.bank_type, ba.priority;
```

### Query คำนวณค่าธรรมเนียม:
```sql
SELECT mfc.transaction_type, mfc.fee_type, mfc.fee_value, mfc.min_fee, mfc.max_fee
FROM mdr_fee_configs mfc
WHERE (mfc.merchant_id = 1 OR mfc.merchant_id IS NULL)
  AND (mfc.agent_id = 701 OR mfc.agent_id IS NULL)
  AND mfc.is_active = 1
  AND NOW() BETWEEN mfc.effective_from AND COALESCE(mfc.effective_to, '2099-12-31')
ORDER BY mfc.merchant_id DESC, mfc.agent_id DESC;
```

## Performance Considerations

### Recommended Indexes:
- `merchant_balances.merchant_id` (UNIQUE)
- `transactions.merchant_id, transaction_type`
- `transactions.txn_date`
- `bank_accounts.agent_id, bank_type`
- `balance_logs.merchant_id, balance_type`

### Query Optimization Tips:
1. ใช้ LIMIT เมื่อ query ข้อมูลจำนวนมาก
2. ใช้ date range ใน WHERE clause สำหรับ transaction queries
3. ใช้ prepared statements สำหรับ queries ที่ใช้บ่อย
4. พิจารณาใช้ read replicas สำหรับ reporting queries
