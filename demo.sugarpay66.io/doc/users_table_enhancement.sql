-- =====================================================
-- Users Table Enhancement สำหรับ SugarPay Profile Features
-- =====================================================

-- เพิ่มฟิลด์ที่จำเป็นสำหรับ Profile Features
ALTER TABLE `users` 
ADD COLUMN `pin_code` varchar(255) DEFAULT NULL COMMENT 'PIN Code สำหรับถอนเงิน (เข้ารหัส)' AFTER `password`,
ADD COLUMN `first_name` varchar(255) DEFAULT NULL COMMENT 'ชื่อจริง' AFTER `name`,
ADD COLUMN `last_name` varchar(255) DEFAULT NULL COMMENT 'นามสกุล' AFTER `first_name`,
ADD COLUMN `bank_name` varchar(100) DEFAULT NULL COMMENT 'ชื่อธนาคารของผู้ใช้' AFTER `phone`,
ADD COLUMN `bank_account_no` varchar(50) DEFAULT NULL COMMENT 'เลขที่บัญชีธนาคารของผู้ใช้' AFTER `bank_name`,
ADD COLUMN `api_key` varchar(255) DEFAULT NULL COMMENT 'API Key สำหรับ Integration' AFTER `bank_account_no`,
ADD COLUMN `secret_key` varchar(255) DEFAULT NULL COMMENT 'Secret Key สำหรับ Integration' AFTER `api_key`,
ADD COLUMN `callback_url` varchar(500) DEFAULT NULL COMMENT 'Default Callback URL' AFTER `secret_key`,
ADD COLUMN `is_auto_cancel_withdraw` tinyint(1) DEFAULT 0 COMMENT 'เปิด/ปิด Auto Cancel Withdraw' AFTER `callback_url`,
ADD COLUMN `password_changed_date` timestamp NULL DEFAULT NULL COMMENT 'วันที่เปลี่ยนรหัสผ่านล่าสุด' AFTER `is_auto_cancel_withdraw`,
ADD COLUMN `pin_code_changed_date` timestamp NULL DEFAULT NULL COMMENT 'วันที่เปลี่ยน PIN Code ล่าสุด' AFTER `password_changed_date`,
ADD COLUMN `google2fa_enabled_date` timestamp NULL DEFAULT NULL COMMENT 'วันที่เปิดใช้ 2FA' AFTER `pin_code_changed_date`,
ADD COLUMN `last_password_change` timestamp NULL DEFAULT NULL COMMENT 'วันที่เปลี่ยนรหัสผ่านล่าสุด (สำหรับแสดงผล)' AFTER `google2fa_enabled_date`;

-- เพิ่ม Index สำหรับ performance
ALTER TABLE `users` 
ADD INDEX `idx_users_api_key` (`api_key`),
ADD INDEX `idx_users_pin_code` (`pin_code`),
ADD INDEX `idx_users_bank_account` (`bank_account_no`);

-- =====================================================
-- เพิ่มตาราง user_api_keys สำหรับจัดการ API Keys แยกต่างหาก
-- =====================================================

CREATE TABLE `user_api_keys` (
  `api_key_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `api_key` varchar(255) NOT NULL UNIQUE,
  `secret_key` varchar(255) NOT NULL,
  `key_name` varchar(100) DEFAULT 'Default API Key',
  `permissions` json DEFAULT NULL COMMENT 'สิทธิ์การใช้งาน API',
  `ip_whitelist` text DEFAULT NULL COMMENT 'IP ที่อนุญาตให้ใช้ API Key นี้',
  `is_active` tinyint(1) DEFAULT 1,
  `expires_at` timestamp NULL DEFAULT NULL COMMENT 'วันหมดอายุ',
  `last_used` timestamp NULL DEFAULT NULL COMMENT 'ใช้งานครั้งล่าสุด',
  `usage_count` int(11) DEFAULT 0 COMMENT 'จำนวนครั้งที่ใช้งาน',
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`api_key_id`),
  UNIQUE KEY `uk_api_key` (`api_key`),
  KEY `idx_user_api_keys_user` (`user_id`),
  KEY `idx_user_api_keys_active` (`is_active`, `expires_at`),
  CONSTRAINT `fk_user_api_keys_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- เพิ่มตาราง user_security_logs สำหรับ Security Audit
-- =====================================================

CREATE TABLE `user_security_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action_type` enum('login','logout','password_change','pin_change','2fa_enable','2fa_disable','api_key_generate','failed_login') NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `additional_data` json DEFAULT NULL COMMENT 'ข้อมูลเพิ่มเติม',
  `is_success` tinyint(1) DEFAULT 1,
  `failure_reason` varchar(255) DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`),
  KEY `idx_user_security_logs_user` (`user_id`, `created_date`),
  KEY `idx_user_security_logs_action` (`action_type`, `created_date`),
  KEY `idx_user_security_logs_ip` (`ip_address`),
  CONSTRAINT `fk_user_security_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- เพิ่มตาราง user_preferences สำหรับการตั้งค่าส่วนตัว
-- =====================================================

CREATE TABLE `user_preferences` (
  `preference_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `preference_key` varchar(100) NOT NULL,
  `preference_value` text DEFAULT NULL,
  `preference_type` enum('string','number','boolean','json') DEFAULT 'string',
  `is_system` tinyint(1) DEFAULT 0 COMMENT 'เป็นการตั้งค่าระบบหรือไม่',
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`preference_id`),
  UNIQUE KEY `uk_user_preference` (`user_id`, `preference_key`),
  CONSTRAINT `fk_user_preferences_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- Stored Procedures สำหรับ User Management
-- =====================================================

DELIMITER //

-- Procedure สำหรับสร้าง API Key ใหม่
CREATE PROCEDURE `sp_generate_user_api_key`(
    IN p_user_id INT,
    IN p_key_name VARCHAR(100),
    IN p_permissions JSON,
    IN p_ip_whitelist TEXT,
    IN p_expires_days INT,
    OUT p_api_key VARCHAR(255),
    OUT p_secret_key VARCHAR(255)
)
BEGIN
    DECLARE v_api_key VARCHAR(255);
    DECLARE v_secret_key VARCHAR(255);
    DECLARE v_expires_at TIMESTAMP;
    
    -- สร้าง API Key และ Secret Key
    SET v_api_key = CONCAT(
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8)
    );
    
    SET v_secret_key = CONCAT(
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8), '-',
        SUBSTRING(MD5(RAND()), 1, 8)
    );
    
    -- คำนวณวันหมดอายุ
    IF p_expires_days > 0 THEN
        SET v_expires_at = DATE_ADD(NOW(), INTERVAL p_expires_days DAY);
    ELSE
        SET v_expires_at = NULL;
    END IF;
    
    -- ปิดการใช้งาน API Key เก่า (ถ้ามี)
    UPDATE user_api_keys SET is_active = 0 WHERE user_id = p_user_id;
    
    -- เพิ่ม API Key ใหม่
    INSERT INTO user_api_keys (
        user_id, api_key, secret_key, key_name, 
        permissions, ip_whitelist, expires_at
    ) VALUES (
        p_user_id, v_api_key, v_secret_key, p_key_name,
        p_permissions, p_ip_whitelist, v_expires_at
    );
    
    -- อัปเดต users table
    UPDATE users SET 
        api_key = v_api_key,
        secret_key = v_secret_key,
        updated_date = NOW()
    WHERE user_id = p_user_id;
    
    -- Return values
    SET p_api_key = v_api_key;
    SET p_secret_key = v_secret_key;
END //

-- Procedure สำหรับบันทึก Security Log
CREATE PROCEDURE `sp_log_user_security_action`(
    IN p_user_id INT,
    IN p_action_type ENUM('login','logout','password_change','pin_change','2fa_enable','2fa_disable','api_key_generate','failed_login'),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT,
    IN p_additional_data JSON,
    IN p_is_success TINYINT(1),
    IN p_failure_reason VARCHAR(255)
)
BEGIN
    INSERT INTO user_security_logs (
        user_id, action_type, ip_address, user_agent,
        additional_data, is_success, failure_reason
    ) VALUES (
        p_user_id, p_action_type, p_ip_address, p_user_agent,
        p_additional_data, p_is_success, p_failure_reason
    );
END //

-- Procedure สำหรับเปลี่ยนรหัสผ่าน
CREATE PROCEDURE `sp_change_user_password`(
    IN p_user_id INT,
    IN p_old_password VARCHAR(255),
    IN p_new_password VARCHAR(255),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT,
    OUT p_result INT
)
BEGIN
    DECLARE v_current_password VARCHAR(255);
    DECLARE v_hashed_password VARCHAR(255);
    
    -- ตรวจสอบรหัสผ่านเก่า
    SELECT password INTO v_current_password 
    FROM users WHERE user_id = p_user_id;
    
    -- ตรวจสอบว่ารหัสผ่านเก่าถูกต้องหรือไม่ (ต้องใช้ password verification function)
    -- สำหรับตัวอย่างนี้ใช้การเปรียบเทียบแบบง่าย
    IF v_current_password = p_old_password THEN
        -- เข้ารหัสรหัสผ่านใหม่ (ในการใช้งานจริงต้องใช้ bcrypt หรือ argon2)
        SET v_hashed_password = p_new_password;
        
        -- อัปเดตรหัสผ่าน
        UPDATE users SET 
            password = v_hashed_password,
            password_changed_date = NOW(),
            last_password_change = NOW(),
            updated_date = NOW()
        WHERE user_id = p_user_id;
        
        -- บันทึก Security Log
        CALL sp_log_user_security_action(
            p_user_id, 'password_change', p_ip_address, p_user_agent,
            NULL, 1, NULL
        );
        
        SET p_result = 0; -- สำเร็จ
    ELSE
        -- บันทึก Security Log สำหรับความล้มเหลว
        CALL sp_log_user_security_action(
            p_user_id, 'password_change', p_ip_address, p_user_agent,
            NULL, 0, 'Invalid old password'
        );
        
        SET p_result = 1; -- รหัสผ่านเก่าไม่ถูกต้อง
    END IF;
END //

-- Procedure สำหรับตั้งค่า PIN Code
CREATE PROCEDURE `sp_set_user_pin_code`(
    IN p_user_id INT,
    IN p_encrypted_pin VARCHAR(255),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT,
    OUT p_result INT
)
BEGIN
    -- อัปเดต PIN Code
    UPDATE users SET 
        pin_code = p_encrypted_pin,
        pin_code_changed_date = NOW(),
        updated_date = NOW()
    WHERE user_id = p_user_id;
    
    -- บันทึก Security Log
    CALL sp_log_user_security_action(
        p_user_id, 'pin_change', p_ip_address, p_user_agent,
        NULL, 1, NULL
    );
    
    SET p_result = 0; -- สำเร็จ
END //

DELIMITER ;

-- =====================================================
-- Views สำหรับ User Management
-- =====================================================

-- View สำหรับ User Profile Summary
CREATE VIEW `v_user_profile_summary` AS
SELECT 
    u.user_id,
    u.username,
    u.first_name,
    u.last_name,
    CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as full_name,
    u.email,
    u.phone,
    u.user_type,
    u.bank_name,
    u.bank_account_no,
    u.api_key,
    u.secret_key,
    u.callback_url,
    u.is_google2fa,
    u.is_auto_cancel_withdraw,
    u.last_login,
    u.password_changed_date,
    u.pin_code_changed_date,
    u.is_active,
    u.is_locked,
    a.agent_name,
    m.merchant_name,
    m.merchant_code
FROM users u
LEFT JOIN agents a ON u.agent_id = a.agent_id
LEFT JOIN merchants m ON u.merchant_id = m.merchant_id
WHERE u.is_delete = 0;

-- View สำหรับ User Security Summary
CREATE VIEW `v_user_security_summary` AS
SELECT 
    u.user_id,
    u.username,
    u.is_google2fa,
    u.login_attempts,
    u.is_locked,
    u.locked_until,
    u.last_login,
    u.password_changed_date,
    u.pin_code_changed_date,
    (SELECT COUNT(*) FROM user_security_logs WHERE user_id = u.user_id AND action_type = 'failed_login' AND created_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as failed_logins_24h,
    (SELECT COUNT(*) FROM user_security_logs WHERE user_id = u.user_id AND created_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as security_events_30d,
    (SELECT created_date FROM user_security_logs WHERE user_id = u.user_id ORDER BY created_date DESC LIMIT 1) as last_security_event
FROM users u
WHERE u.is_delete = 0;

-- =====================================================
-- Default User Preferences
-- =====================================================

-- เพิ่มการตั้งค่าเริ่มต้นสำหรับ User ใหม่
INSERT INTO admin_settings (setting_key, setting_value, setting_type, description) VALUES
('default_api_key_expires_days', '365', 'number', 'จำนวนวันที่ API Key หมดอายุ (0 = ไม่หมดอายุ)'),
('require_pin_for_withdraw', 'true', 'boolean', 'บังคับใช้ PIN Code สำหรับการถอนเงิน'),
('max_failed_login_attempts', '5', 'number', 'จำนวนครั้งสูงสุดที่อนุญาตให้ login ผิด'),
('account_lockout_duration_minutes', '30', 'number', 'ระยะเวลาล็อกบัญชี (นาที)'),
('force_password_change_days', '90', 'number', 'บังคับเปลี่ยนรหัสผ่านทุกกี่วัน (0 = ไม่บังคับ)');

-- =====================================================
-- Triggers สำหรับ User Management
-- =====================================================

DELIMITER //

-- Trigger สำหรับสร้าง Default Preferences เมื่อสร้าง User ใหม่
CREATE TRIGGER `tr_create_user_defaults` 
AFTER INSERT ON `users`
FOR EACH ROW
BEGIN
    -- สร้าง Default Preferences
    INSERT INTO user_preferences (user_id, preference_key, preference_value, preference_type) VALUES
    (NEW.user_id, 'theme', 'light', 'string'),
    (NEW.user_id, 'language', 'th', 'string'),
    (NEW.user_id, 'timezone', 'Asia/Bangkok', 'string'),
    (NEW.user_id, 'notifications_email', 'true', 'boolean'),
    (NEW.user_id, 'notifications_sms', 'false', 'boolean');
    
    -- บันทึก Security Log สำหรับการสร้าง User
    INSERT INTO user_security_logs (user_id, action_type, additional_data, is_success) VALUES
    (NEW.user_id, 'login', JSON_OBJECT('event', 'user_created'), 1);
END //

DELIMITER ;
