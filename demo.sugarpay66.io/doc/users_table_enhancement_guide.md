# คู่มือการแก้ไข Users Table สำหรับ SugarPay Profile Features

## ภาพรวม

จากการตรวจสอบไฟล์ `profile.html` พบว่าต้องแก้ไข table `users` เพื่อรองรับฟีเจอร์ต่างๆ ในหน้า Profile ดังนี้:

## 🔍 **ฟีเจอร์ที่พบในหน้า Profile**

### 1. **Withdraw Pin Code**
- ตั้งค่า PIN Code 6 หลักสำหรับการถอนเงิน
- เข้ารหัสด้วย CryptoJS AES
- มีการยืนยัน PIN Code

### 2. **Profile Information**
- First Name, Last Name
- Email, Telephone
- Bank Name, Bank Account Number
- Merchant Name
- MDR Rates (Deposit/Withdraw)

### 3. **API Integration**
- API Key และ Secret Key
- Copy to clipboard functionality
- Show/Hide Secret Key

### 4. **Change Password**
- เปลี่ยนรหัสผ่าน
- แสดงวันที่เปลี่ยนรหัสผ่านล่าสุด
- ตรวจสอบรหัสผ่านเก่า

### 5. **Callback URL**
- ตั้งค่า Default Callback URL
- สำหรับรับ webhook notifications

### 6. **Auto Cancel Withdraw**
- เปิด/ปิดการยกเลิกธุรกรรมถอนอัตโนมัติ
- เมื่อมีการปิดระบบชั่วคราว

### 7. **Google 2FA**
- เปิด/ปิด Google Authenticator
- Two-Factor Authentication

## 📊 **การแก้ไข Users Table**

### **ฟิลด์ใหม่ที่เพิ่ม**

| ฟิลด์ | ประเภท | คำอธิบาย |
|------|--------|----------|
| `pin_code` | VARCHAR(255) | PIN Code สำหรับถอนเงิน (เข้ารหัส) |
| `first_name` | VARCHAR(255) | ชื่อจริง |
| `last_name` | VARCHAR(255) | นามสกุล |
| `bank_name` | VARCHAR(100) | ชื่อธนาคารของผู้ใช้ |
| `bank_account_no` | VARCHAR(50) | เลขที่บัญชีธนาคาร |
| `api_key` | VARCHAR(255) | API Key สำหรับ Integration |
| `secret_key` | VARCHAR(255) | Secret Key สำหรับ Integration |
| `callback_url` | VARCHAR(500) | Default Callback URL |
| `is_auto_cancel_withdraw` | TINYINT(1) | เปิด/ปิด Auto Cancel Withdraw |
| `password_changed_date` | TIMESTAMP | วันที่เปลี่ยนรหัสผ่านล่าสุด |
| `pin_code_changed_date` | TIMESTAMP | วันที่เปลี่ยน PIN Code ล่าสุด |
| `google2fa_enabled_date` | TIMESTAMP | วันที่เปิดใช้ 2FA |
| `last_password_change` | TIMESTAMP | วันที่เปลี่ยนรหัสผ่านล่าสุด (สำหรับแสดงผล) |

## 🗄️ **ตารางเสริมใหม่**

### 1. **user_api_keys** - จัดการ API Keys
```sql
CREATE TABLE `user_api_keys` (
  `api_key_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `api_key` varchar(255) NOT NULL UNIQUE,
  `secret_key` varchar(255) NOT NULL,
  `key_name` varchar(100) DEFAULT 'Default API Key',
  `permissions` json DEFAULT NULL,
  `ip_whitelist` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `expires_at` timestamp NULL DEFAULT NULL,
  `last_used` timestamp NULL DEFAULT NULL,
  `usage_count` int(11) DEFAULT 0,
  ...
);
```

### 2. **user_security_logs** - Security Audit Trail
```sql
CREATE TABLE `user_security_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action_type` enum('login','logout','password_change','pin_change','2fa_enable','2fa_disable','api_key_generate','failed_login') NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `additional_data` json DEFAULT NULL,
  `is_success` tinyint(1) DEFAULT 1,
  `failure_reason` varchar(255) DEFAULT NULL,
  ...
);
```

### 3. **user_preferences** - การตั้งค่าส่วนตัว
```sql
CREATE TABLE `user_preferences` (
  `preference_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `preference_key` varchar(100) NOT NULL,
  `preference_value` text DEFAULT NULL,
  `preference_type` enum('string','number','boolean','json') DEFAULT 'string',
  `is_system` tinyint(1) DEFAULT 0,
  ...
);
```

## 🔧 **Stored Procedures ใหม่**

### 1. **sp_generate_user_api_key** - สร้าง API Key
```sql
CALL sp_generate_user_api_key(
    1,                    -- user_id
    'Default API Key',    -- key_name
    '{"read": true, "write": true}', -- permissions
    '***********/24',    -- ip_whitelist
    365,                 -- expires_days
    @api_key,           -- OUT api_key
    @secret_key         -- OUT secret_key
);
```

### 2. **sp_change_user_password** - เปลี่ยนรหัสผ่าน
```sql
CALL sp_change_user_password(
    1,                   -- user_id
    'old_password',      -- old_password
    'new_password',      -- new_password
    '*************',    -- ip_address
    'Mozilla/5.0...',   -- user_agent
    @result             -- OUT result (0=success, 1=fail)
);
```

### 3. **sp_set_user_pin_code** - ตั้งค่า PIN Code
```sql
CALL sp_set_user_pin_code(
    1,                   -- user_id
    'encrypted_pin',     -- encrypted_pin
    '*************',    -- ip_address
    'Mozilla/5.0...',   -- user_agent
    @result             -- OUT result
);
```

## 📊 **Views ใหม่**

### 1. **v_user_profile_summary** - สรุปข้อมูล Profile
```sql
SELECT * FROM v_user_profile_summary WHERE user_id = 1;
```

### 2. **v_user_security_summary** - สรุปความปลอดภัย
```sql
SELECT * FROM v_user_security_summary WHERE user_id = 1;
```

## 🚀 **การติดตั้ง**

### 1. **รันไฟล์ Enhancement**
```bash
mysql -u root -p sugarpay_db < doc/users_table_enhancement.sql
```

### 2. **ตรวจสอบการติดตั้ง**
```sql
-- ตรวจสอบฟิลด์ใหม่
DESCRIBE users;

-- ตรวจสอบตารางใหม่
SHOW TABLES LIKE 'user_%';

-- ตรวจสอบ Stored Procedures
SHOW PROCEDURE STATUS WHERE Db = 'sugarpay_db' AND Name LIKE 'sp_%user%';

-- ตรวจสอบ Views
SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_sugarpay_db LIKE 'v_user_%';
```

## 💡 **ตัวอย่างการใช้งาน**

### 1. **สร้าง User ใหม่พร้อมข้อมูลครบถ้วน**
```sql
INSERT INTO users (
    agent_id, username, password, first_name, last_name, 
    email, phone, user_type, bank_name, bank_account_no
) VALUES (
    1, 'tiger-001', '$2y$10$hashed_password', 'Tiger', 'Restaurant',
    '<EMAIL>', '**********', 'merchant', 
    'WAIT_BANK_ACCOUNT', '**********'
);
```

### 2. **สร้าง API Key สำหรับ User**
```sql
CALL sp_generate_user_api_key(
    1, 'Tiger API Key', 
    '{"transactions": true, "balance": true}',
    '*************,203.154.1.1',
    365, @api_key, @secret_key
);

SELECT @api_key, @secret_key;
```

### 3. **ตั้งค่า PIN Code**
```sql
-- เข้ารหัส PIN ด้วย CryptoJS ก่อน (ในฝั่ง Frontend)
CALL sp_set_user_pin_code(
    1, 'encrypted_pin_code_here',
    '*************', 'Mozilla/5.0...',
    @result
);
```

### 4. **เปลี่ยนรหัสผ่าน**
```sql
CALL sp_change_user_password(
    1, 'old_password', 'new_password',
    '*************', 'Mozilla/5.0...',
    @result
);
```

### 5. **ดูข้อมูล Profile**
```sql
SELECT 
    username, full_name, email, phone,
    bank_name, bank_account_no,
    api_key, secret_key, callback_url,
    is_google2fa, is_auto_cancel_withdraw,
    password_changed_date
FROM v_user_profile_summary 
WHERE username = 'tiger-001';
```

### 6. **ดูประวัติความปลอดภัย**
```sql
SELECT 
    action_type, ip_address, is_success,
    failure_reason, created_date
FROM user_security_logs 
WHERE user_id = 1 
ORDER BY created_date DESC 
LIMIT 10;
```

## 🔐 **ความปลอดภัย**

### 1. **การเข้ารหัส**
- **PIN Code**: เข้ารหัสด้วย CryptoJS AES
- **Password**: ใช้ bcrypt หรือ argon2 (ต้องแก้ไขใน Stored Procedure)
- **API Keys**: สร้างแบบสุ่มและไม่ซ้ำ

### 2. **Audit Trail**
- บันทึกการเปลี่ยนแปลงทั้งหมดใน `user_security_logs`
- เก็บ IP Address และ User Agent
- ติดตามการใช้งาน API Keys

### 3. **Access Control**
- IP Whitelist สำหรับ API Keys
- การหมดอายุของ API Keys
- การล็อกบัญชีเมื่อ login ผิดหลายครั้ง

## 📋 **Checklist การแก้ไข**

- ✅ เพิ่มฟิลด์ใหม่ใน users table
- ✅ สร้างตาราง user_api_keys
- ✅ สร้างตาราง user_security_logs  
- ✅ สร้างตาราง user_preferences
- ✅ สร้าง Stored Procedures
- ✅ สร้าง Views
- ✅ เพิ่ม Triggers
- ✅ เพิ่ม Admin Settings
- ✅ เพิ่ม Indexes สำหรับ Performance

## 🔄 **การ Migrate ข้อมูลเก่า**

หากมีข้อมูล users เก่าอยู่แล้ว:

```sql
-- อัปเดตข้อมูลเก่า
UPDATE users SET 
    first_name = SUBSTRING_INDEX(name, ' ', 1),
    last_name = SUBSTRING_INDEX(name, ' ', -1),
    bank_name = 'WAIT_BANK_ACCOUNT',
    bank_account_no = '**********'
WHERE first_name IS NULL;

-- สร้าง API Keys สำหรับ users ที่มีอยู่
INSERT INTO user_api_keys (user_id, api_key, secret_key, key_name)
SELECT 
    user_id,
    CONCAT(SUBSTRING(MD5(RAND()), 1, 8), '-', SUBSTRING(MD5(RAND()), 1, 8), '-', SUBSTRING(MD5(RAND()), 1, 8), '-', SUBSTRING(MD5(RAND()), 1, 8)),
    CONCAT(SUBSTRING(MD5(RAND()), 1, 8), '-', SUBSTRING(MD5(RAND()), 1, 8), '-', SUBSTRING(MD5(RAND()), 1, 8), '-', SUBSTRING(MD5(RAND()), 1, 8)),
    'Default API Key'
FROM users 
WHERE is_delete = 0;
```

## 🎯 **สรุป**

การแก้ไข users table นี้จะทำให้ระบบ SugarPay รองรับฟีเจอร์ในหน้า Profile ได้ครบถ้วน:

- ✅ **PIN Code Management**
- ✅ **API Key Management** 
- ✅ **Profile Information**
- ✅ **Security Features**
- ✅ **User Preferences**
- ✅ **Audit Trail**

ระบบจะมีความปลอดภัยสูงขึ้นและสามารถติดตามการใช้งานได้อย่างละเอียด!
